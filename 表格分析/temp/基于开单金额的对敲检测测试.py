#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于开单金额的对敲检测测试 - 1000个订单，10U-50万U范围
"""

import random
import math

def calculate_hedge_score_with_order_amount(profit_a, profit_b, order_amount_a, order_amount_b, expected_scale=None):
    """基于开单金额的对敲检测算法"""
    
    # 基础检查
    profit_sum = abs(profit_a) + abs(profit_b)
    order_sum = order_amount_a + order_amount_b
    
    if profit_sum == 0:
        return 0.0, {"risk_level": "正常交易", "explanation": "无盈亏"}
    
    if order_sum == 0:
        return 0.0, {"risk_level": "正常交易", "explanation": "无开单金额"}
    
    # 判断同边/异边
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        # 异边情况：使用原始抵消度公式
        total_profit = profit_a + profit_b
        score = 1.0 - abs(total_profit) / profit_sum
        case_type = "异边"
        
    else:
        # 同边情况：基于开单金额的优化算法
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        # 使用真实开单金额作为规模
        scale = order_sum
        
        # 因子1：接近0程度评分（基于真实规模）
        min_relative_to_order = min_abs / scale
        if min_relative_to_order <= 0.001:
            closeness_score = 0.95
        elif min_relative_to_order <= 0.005:
            closeness_score = 0.9
        elif min_relative_to_order <= 0.01:
            closeness_score = 0.8
        elif min_relative_to_order <= 0.02:
            closeness_score = 0.6
        elif min_relative_to_order <= 0.05:
            closeness_score = 0.4
        elif min_relative_to_order <= 0.1:
            closeness_score = 0.2
        else:
            closeness_score = 0.1
        
        # 因子2：相对波动率评分
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:
            volatility_score = 0.9
        elif max_relative <= 0.03:
            volatility_score = 0.8
        elif max_relative <= 0.05:
            volatility_score = 0.6
        elif max_relative <= 0.1:
            volatility_score = 0.4
        else:
            volatility_score = 0.2
        
        # 因子3：差异度评分
        diff_ratio = abs(abs_a - abs_b) / max_abs
        if diff_ratio >= 0.8:
            diff_score = 0.9
        elif diff_ratio >= 0.5:
            diff_score = 0.7
        elif diff_ratio >= 0.2:
            diff_score = 0.5
        else:
            diff_score = 0.3
        
        # 因子4：开单金额对称性评分
        order_ratio = min(order_amount_a, order_amount_b) / max(order_amount_a, order_amount_b)
        if order_ratio >= 0.9:
            order_symmetry_score = 0.9
        elif order_ratio >= 0.8:
            order_symmetry_score = 0.8
        elif order_ratio >= 0.6:
            order_symmetry_score = 0.6
        elif order_ratio >= 0.4:
            order_symmetry_score = 0.4
        else:
            order_symmetry_score = 0.2
        
        # 因子5：盈亏率对比评分
        profit_rate_a = abs_a / order_amount_a if order_amount_a > 0 else 0
        profit_rate_b = abs_b / order_amount_b if order_amount_b > 0 else 0
        
        min_profit_rate = min(profit_rate_a, profit_rate_b)
        max_profit_rate = max(profit_rate_a, profit_rate_b)
        
        if min_profit_rate <= 0.005 and max_profit_rate >= 0.02:
            profit_rate_score = 0.95
        elif min_profit_rate <= 0.01 and max_profit_rate >= 0.015:
            profit_rate_score = 0.9
        elif min_profit_rate <= 0.02 and max_profit_rate >= 0.03:
            profit_rate_score = 0.8
        elif min_profit_rate <= 0.03 and max_profit_rate >= 0.05:
            profit_rate_score = 0.6
        else:
            if max_profit_rate <= 0.01:
                profit_rate_score = 0.3
            else:
                profit_rate_score = 0.2
        
        # 五因子动态权重计算
        if min_profit_rate <= 0.005:
            score = (0.4 * profit_rate_score + 
                    0.3 * closeness_score + 
                    0.15 * order_symmetry_score + 
                    0.1 * volatility_score + 
                    0.05 * diff_score)
        elif min_profit_rate <= 0.02:
            score = (0.3 * profit_rate_score + 
                    0.25 * closeness_score + 
                    0.2 * order_symmetry_score + 
                    0.15 * volatility_score + 
                    0.1 * diff_score)
        else:
            score = (0.15 * profit_rate_score + 
                    0.15 * closeness_score + 
                    0.3 * order_symmetry_score + 
                    0.2 * volatility_score + 
                    0.2 * diff_score)
        
        case_type = "双盈" if profit_a > 0 else "双亏"
    
    # 风险阈值判断
    if case_type in ["双盈", "双亏"]:
        if score >= 0.6:
            risk_level = "高风险对敲"
        elif score >= 0.45:
            risk_level = "中等风险对敲"
        elif score >= 0.3:
            risk_level = "低风险对敲"
        else:
            risk_level = "正常交易"
    else:
        if score >= 0.8:
            risk_level = "高风险对敲"
        elif score >= 0.6:
            risk_level = "中等风险对敲"
        elif score >= 0.4:
            risk_level = "低风险对敲"
        else:
            risk_level = "正常交易"
    
    details = {
        "score": score,
        "risk_level": risk_level,
        "case_type": case_type,
        "is_same_side": is_same_side,
        "profit_rate_a": profit_rate_a if 'profit_rate_a' in locals() else None,
        "profit_rate_b": profit_rate_b if 'profit_rate_b' in locals() else None,
        "order_symmetry": order_ratio if 'order_ratio' in locals() else None
    }
    
    return score, details


def generate_order_based_test_cases():
    """生成基于开单金额的测试用例"""
    
    test_cases = []
    order_scales = [10, 50, 100, 500, 1000, 5000, 10000, 50000, 100000, 500000]
    case_id = 1
    
    for base_order in order_scales:
        # === 1. 异边情况（一盈一亏）===
        for _ in range(10):
            # 高风险对敲：开单相近，盈亏接近抵消
            order_a = random.uniform(0.8 * base_order, 1.2 * base_order)
            order_b = random.uniform(0.8 * base_order, 1.2 * base_order)
            
            profit_a = random.uniform(0.01 * order_a, 0.05 * order_a)  # 1%-5%盈利
            profit_b = -random.uniform(0.8 * profit_a, 1.2 * profit_a)  # 接近抵消的亏损
            
            test_cases.append((case_id, profit_a, profit_b, order_a, order_b, base_order, "异边-高风险"))
            case_id += 1
        
        # === 2. 同边双亏-高风险对敲 ===
        for _ in range(15):
            # 典型对敲：一方几乎无亏损，另一方正常亏损，开单金额相近
            order_a = random.uniform(0.9 * base_order, 1.1 * base_order)
            order_b = random.uniform(0.9 * base_order, 1.1 * base_order)
            
            # 一方盈亏率很低（≤0.5%）
            profit_a = -random.uniform(0.0001 * order_a, 0.005 * order_a)
            # 另一方盈亏率正常（2%-8%）
            profit_b = -random.uniform(0.02 * order_b, 0.08 * order_b)
            
            test_cases.append((case_id, profit_a, profit_b, order_a, order_b, base_order, "同边双亏-高风险"))
            case_id += 1
        
        # === 3. 同边双亏-中等风险对敲 ===
        for _ in range(10):
            # 一方盈亏率较低（0.5%-2%），另一方正常
            order_a = random.uniform(0.8 * base_order, 1.2 * base_order)
            order_b = random.uniform(0.8 * base_order, 1.2 * base_order)
            
            profit_a = -random.uniform(0.005 * order_a, 0.02 * order_a)
            profit_b = -random.uniform(0.03 * order_b, 0.1 * order_b)
            
            test_cases.append((case_id, profit_a, profit_b, order_a, order_b, base_order, "同边双亏-中等风险"))
            case_id += 1
        
        # === 4. 同边双亏-正常交易 ===
        for _ in range(10):
            # 两方盈亏率都正常（3%-15%）
            order_a = random.uniform(0.5 * base_order, 1.5 * base_order)
            order_b = random.uniform(0.5 * base_order, 1.5 * base_order)
            
            profit_a = -random.uniform(0.03 * order_a, 0.15 * order_a)
            profit_b = -random.uniform(0.03 * order_b, 0.15 * order_b)
            
            test_cases.append((case_id, profit_a, profit_b, order_a, order_b, base_order, "同边双亏-正常交易"))
            case_id += 1
        
        # === 5. 同边双盈情况 ===
        for _ in range(15):
            # 高风险：一方盈利率很低，另一方正常盈利
            order_a = random.uniform(0.9 * base_order, 1.1 * base_order)
            order_b = random.uniform(0.9 * base_order, 1.1 * base_order)
            
            profit_a = random.uniform(0.0001 * order_a, 0.005 * order_a)
            profit_b = random.uniform(0.02 * order_b, 0.08 * order_b)
            
            test_cases.append((case_id, profit_a, profit_b, order_a, order_b, base_order, "同边双盈-高风险"))
            case_id += 1
    
    return test_cases


def run_order_based_test():
    """运行基于开单金额的测试"""
    
    print("=== 基于开单金额的对敲检测测试 ===")
    print("测试范围：10U-50万U，包含开单金额信息")
    
    test_cases = generate_order_based_test_cases()
    print(f"生成 {len(test_cases)} 个测试用例\n")
    
    results = []
    
    for case_id, profit_a, profit_b, order_a, order_b, base_order, expected_type in test_cases:
        score, details = calculate_hedge_score_with_order_amount(profit_a, profit_b, order_a, order_b)
        results.append((case_id, profit_a, profit_b, order_a, order_b, base_order, expected_type, score, details["risk_level"]))
    
    # 分析结果
    analyze_order_based_results(results)


def analyze_order_based_results(results):
    """分析基于开单金额的测试结果"""
    
    print("=== 基于开单金额的测试结果分析 ===\n")
    
    # 按预期类型分组统计
    type_stats = {}
    scale_stats = {}
    
    for case_id, profit_a, profit_b, order_a, order_b, base_order, expected_type, score, risk_level in results:
        # 类型统计
        if expected_type not in type_stats:
            type_stats[expected_type] = {
                'count': 0,
                'scores': [],
                'high_risk_count': 0,
                'avg_score': 0
            }
        
        type_stats[expected_type]['count'] += 1
        type_stats[expected_type]['scores'].append(score)
        if risk_level == "高风险对敲":
            type_stats[expected_type]['high_risk_count'] += 1
        
        # 规模统计
        if base_order not in scale_stats:
            scale_stats[base_order] = {'count': 0, 'avg_score': 0, 'scores': []}
        scale_stats[base_order]['count'] += 1
        scale_stats[base_order]['scores'].append(score)
    
    # 计算平均分
    for type_name in type_stats:
        stats = type_stats[type_name]
        stats['avg_score'] = sum(stats['scores']) / len(stats['scores'])
    
    for scale in scale_stats:
        stats = scale_stats[scale]
        stats['avg_score'] = sum(stats['scores']) / len(stats['scores'])
    
    # 输出详细结果
    print("按预期类型统计：")
    print(f"{'类型':<20} {'案例数':<8} {'平均评分':<10} {'高风险识别率':<15}")
    print("-" * 60)
    
    for type_name in sorted(type_stats.keys()):
        stats = type_stats[type_name]
        high_risk_rate = f"{stats['high_risk_count']}/{stats['count']} ({stats['high_risk_count']/stats['count']*100:.0f}%)"
        print(f"{type_name:<20} {stats['count']:<8} {stats['avg_score']:<10.3f} {high_risk_rate:<15}")
    
    print(f"\n按开单规模统计：")
    print(f"{'规模':<10} {'案例数':<8} {'平均评分':<10}")
    print("-" * 35)
    
    for scale in sorted(scale_stats.keys()):
        stats = scale_stats[scale]
        print(f"{scale:<10} {stats['count']:<8} {stats['avg_score']:<10.3f}")
    
    # 重点分析高风险对敲识别效果
    print(f"\n=== 重点：高风险对敲识别效果 ===")
    
    high_risk_types = [k for k in type_stats.keys() if "高风险" in k]
    total_high_risk_cases = sum(type_stats[t]['count'] for t in high_risk_types)
    total_high_risk_identified = sum(type_stats[t]['high_risk_count'] for t in high_risk_types)
    
    print(f"高风险对敲总体识别率: {total_high_risk_identified}/{total_high_risk_cases} ({total_high_risk_identified/total_high_risk_cases*100:.1f}%)")
    
    for type_name in high_risk_types:
        stats = type_stats[type_name]
        print(f"  {type_name}: {stats['avg_score']:.3f}分, {stats['high_risk_count']}/{stats['count']}识别 ({stats['high_risk_count']/stats['count']*100:.0f}%)")
    
    # 显示一些典型案例
    print(f"\n=== 典型案例展示 ===")
    
    # 按评分排序
    results_sorted = sorted(results, key=lambda x: x[7], reverse=True)
    
    print(f"\n最高评分案例（前5个）：")
    for i, (case_id, profit_a, profit_b, order_a, order_b, base_order, expected_type, score, risk_level) in enumerate(results_sorted[:5]):
        profit_rate_a = abs(profit_a) / order_a * 100
        profit_rate_b = abs(profit_b) / order_b * 100
        print(f"  {i+1}. {expected_type} | 评分:{score:.3f} | 开单:{order_a:.0f}+{order_b:.0f}U | 盈亏率:{profit_rate_a:.2f}%+{profit_rate_b:.2f}% | {risk_level}")
    
    print(f"\n最低评分案例（后5个）：")
    for i, (case_id, profit_a, profit_b, order_a, order_b, base_order, expected_type, score, risk_level) in enumerate(results_sorted[-5:]):
        profit_rate_a = abs(profit_a) / order_a * 100
        profit_rate_b = abs(profit_b) / order_b * 100
        print(f"  {i+1}. {expected_type} | 评分:{score:.3f} | 开单:{order_a:.0f}+{order_b:.0f}U | 盈亏率:{profit_rate_a:.2f}%+{profit_rate_b:.2f}% | {risk_level}")


if __name__ == "__main__":
    run_order_based_test()
