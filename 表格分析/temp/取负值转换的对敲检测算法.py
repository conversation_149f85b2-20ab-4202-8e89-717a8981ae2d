#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
取负值转换的对敲检测算法
核心思路：同边情况下，把最接近0的一方取负值，然后套入原始公式
"""

from dataclasses import dataclass
from typing import Tuple, Optional
import math

@dataclass
class Position:
    """仓位信息"""
    real_profit: float  # 实际盈亏
    volume: float = 0   # 交易量/交易规模
    direction: str = '' # 方向

def calculate_negative_transform_hedge_score(pos_a: Position, pos_b: Position, 
                                           expected_scale: float = None) -> Tuple[float, dict]:
    """
    取负值转换的对敲检测算法
    
    核心思路：
    1. 异边（一盈一亏）：直接使用原始公式
    2. 同边（双盈/双亏）：把最接近0的一方取负值，转换为异边，然后使用原始公式
    
    优势：
    - 保持原始公式的简洁性
    - 统一处理逻辑
    - 自然体现"最接近0"的对敲特征
    
    Args:
        pos_a: 仓位A
        pos_b: 仓位B  
        expected_scale: 预期交易规模（可选，用于额外判断）
    
    Returns:
        Tuple[float, dict]: (最终评分, 详细信息)
    """
    
    pos_a_profit = pos_a.real_profit
    pos_b_profit = pos_b.real_profit
    
    # === 基础检查 ===
    if pos_a_profit == 0 and pos_b_profit == 0:
        return 0.0, {
            'original_a': pos_a_profit,
            'original_b': pos_b_profit,
            'transformed_a': pos_a_profit,
            'transformed_b': pos_b_profit,
            'final_score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'transform_type': '无盈亏',
            'risk_level': '正常交易'
        }
    
    # === 判断是否同边 ===
    is_same_side = (pos_a_profit > 0 and pos_b_profit > 0) or (pos_a_profit < 0 and pos_b_profit < 0)
    
    if not is_same_side:
        # === 异边情况：直接使用原始公式 ===
        transformed_a = pos_a_profit
        transformed_b = pos_b_profit
        transform_type = "异边(一盈一亏)"
        transform_desc = "无需转换"
        
    else:
        # === 同边情况：把最接近0的一方取负值 ===
        abs_a = abs(pos_a_profit)
        abs_b = abs(pos_b_profit)
        
        if abs_a <= abs_b:
            # A更接近0，把A取负值
            transformed_a = -pos_a_profit
            transformed_b = pos_b_profit
            closer_to_zero = "A"
        else:
            # B更接近0，把B取负值
            transformed_a = pos_a_profit
            transformed_b = -pos_b_profit
            closer_to_zero = "B"
        
        if pos_a_profit > 0:
            transform_type = "同边(双盈)"
        else:
            transform_type = "同边(双亏)"
        
        transform_desc = f"将更接近0的仓位{closer_to_zero}取负值"
    
    # === 使用原始公式计算 ===
    total_profit = transformed_a + transformed_b
    profit_sum = abs(transformed_a) + abs(transformed_b)
    
    if profit_sum > 0:
        profit_hedge_score = 1.0 - abs(total_profit) / profit_sum
    else:
        profit_hedge_score = 0.0
    
    # === 额外考虑交易规模（可选增强） ===
    final_score = profit_hedge_score
    scale_adjustment = ""
    
    if expected_scale and expected_scale > 0:
        # 计算相对波动率作为额外参考
        max_abs_original = max(abs(pos_a_profit), abs(pos_b_profit))
        relative_volatility = max_abs_original / expected_scale
        
        # 如果相对波动率过大，适当降分
        if relative_volatility > 0.5:  # >50%
            scale_penalty = 0.5
            scale_adjustment = f"，相对波动率{relative_volatility:.1%}过大，降分至{final_score * scale_penalty:.3f}"
            final_score *= scale_penalty
        elif relative_volatility > 0.2:  # >20%
            scale_penalty = 0.8
            scale_adjustment = f"，相对波动率{relative_volatility:.1%}较大，降分至{final_score * scale_penalty:.3f}"
            final_score *= scale_penalty
    
    # 风险等级
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    # 详细说明
    explanation = (f"{transform_type}，{transform_desc}，"
                  f"转换后({transformed_a:.3f}, {transformed_b:.3f})，"
                  f"原始公式评分:{profit_hedge_score:.3f}{scale_adjustment}")
    
    details = {
        'original_a': pos_a_profit,
        'original_b': pos_b_profit,
        'transformed_a': transformed_a,
        'transformed_b': transformed_b,
        'total_profit': total_profit,
        'profit_sum': profit_sum,
        'profit_hedge_score': profit_hedge_score,
        'final_score': final_score,
        'explanation': explanation,
        'transform_type': transform_type,
        'transform_desc': transform_desc,
        'risk_level': risk_level,
        'is_same_side': is_same_side
    }
    
    return final_score, details


def test_negative_transform_algorithm():
    """测试取负值转换算法"""
    
    print("=== 取负值转换的对敲检测算法测试 ===\n")
    print("核心思路：")
    print("1. 异边（一盈一亏）：直接使用原始公式")
    print("2. 同边（双盈/双亏）：把最接近0的一方取负值，转换为异边，然后使用原始公式")
    print("3. 优势：保持原始公式简洁性，统一处理逻辑\n")
    
    test_cases = [
        # === 异边情况（无需转换）===
        (Position(10.0), Position(-9.5), 100.0, "异边-几乎完全抵消"),
        (Position(10.0), Position(-8.0), 100.0, "异边-大部分抵消"),
        (Position(1.0), Position(-0.9), 100.0, "异边-小金额"),
        
        # === 同边双亏情况（需要转换）===
        (Position(-0.03), Position(-1.0), 10.0, "同边双亏-你的例子(10U规模)"),
        (Position(-0.03), Position(-1.0), 100.0, "同边双亏-你的例子(100U规模)"),
        (Position(-0.1), Position(-5.0), 100.0, "同边双亏-一方很接近0"),
        (Position(-1.0), Position(-10.0), 100.0, "同边双亏-一方接近0"),
        (Position(-2.0), Position(-3.0), 100.0, "同边双亏-都较接近0"),
        (Position(-20.0), Position(-20.0), 100.0, "同边双亏-都远离0且相同"),
        (Position(-15.0), Position(-25.0), 100.0, "同边双亏-都远离0"),
        (Position(-50.0), Position(-60.0), 100.0, "同边双亏-都很远离0"),
        
        # === 同边双盈情况（需要转换）===
        (Position(0.1), Position(5.0), 100.0, "同边双盈-一方很接近0"),
        (Position(1.0), Position(10.0), 100.0, "同边双盈-一方接近0"),
        (Position(2.0), Position(3.0), 100.0, "同边双盈-都较接近0"),
        (Position(20.0), Position(20.0), 100.0, "同边双盈-都远离0且相同"),
        (Position(15.0), Position(25.0), 100.0, "同边双盈-都远离0"),
        
        # === 特殊情况 ===
        (Position(-0.01), Position(-0.02), 100.0, "同边双亏-都极接近0"),
        (Position(0.01), Position(0.02), 100.0, "同边双盈-都极接近0"),
        (Position(-100.0), Position(-0.5), 1000.0, "同边双亏-大金额但一方接近0"),
        
        # === 测试相对波动率影响 ===
        (Position(-10.0), Position(-15.0), 100.0, "同边双亏-中等金额中等波动"),
        (Position(-30.0), Position(-40.0), 100.0, "同边双亏-大金额大波动"),
        (Position(-60.0), Position(-70.0), 100.0, "同边双亏-很大金额很大波动"),
    ]
    
    for i, (pos_a, pos_b, scale, description) in enumerate(test_cases, 1):
        score, details = calculate_negative_transform_hedge_score(pos_a, pos_b, scale)
        
        print(f"测试用例 {i}: {description}")
        print(f"原始仓位: A={details['original_a']}, B={details['original_b']} (规模{scale}U)")
        print(f"转换类型: {details['transform_type']}")
        print(f"转换说明: {details['transform_desc']}")
        print(f"转换后仓位: A={details['transformed_a']:.3f}, B={details['transformed_b']:.3f}")
        print(f"原始公式评分: {details['profit_hedge_score']:.3f}")
        print(f"最终评分: {details['final_score']:.3f}")
        print(f"风险等级: {details['risk_level']}")
        print(f"详细说明: {details['explanation']}")
        print("-" * 80)


if __name__ == "__main__":
    test_negative_transform_algorithm()
