#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心对敲检测算法 - 简化版本
修正原始算法的逻辑问题
"""

def calculate_hedge_score(pos_a_profit, pos_b_profit, pos_a_volume=None, pos_b_volume=None, 
                         pos_a_direction=None, pos_b_direction=None):
    """
    改进的对敲检测算法
    
    Args:
        pos_a_profit: 仓位A的实际盈亏
        pos_b_profit: 仓位B的实际盈亏  
        pos_a_volume: 仓位A的交易量（可选）
        pos_b_volume: 仓位B的交易量（可选）
        pos_a_direction: 仓位A的方向 'long'/'short'（可选）
        pos_b_direction: 仓位B的方向 'long'/'short'（可选）
    
    Returns:
        dict: 包含评分和详细信息的字典
    """
    
    # 1. 基础盈利相关性计算
    total_profit = pos_a_profit + pos_b_profit
    profit_sum = abs(pos_a_profit) + abs(pos_b_profit)
    
    if profit_sum == 0:
        return {
            'score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'risk_level': '无法判断'
        }
    
    # 基础对冲评分：总盈亏越接近0，评分越高
    base_score = 1.0 - abs(total_profit) / profit_sum
    
    # 2. 根据盈亏组合情况调整评分
    if pos_a_profit > 0 and pos_b_profit > 0:
        # 双盈情况：差距越小，对敲可能性越高
        profit_diff = abs(pos_a_profit - pos_b_profit)
        avg_profit = (pos_a_profit + pos_b_profit) / 2
        if avg_profit > 0:
            similarity = 1.0 - (profit_diff / avg_profit)  # 相似度
            if similarity > 0.8:  # 盈利金额非常相近
                adjusted_score = base_score * 0.9  # 高权重，可能是对敲
                explanation = f"双盈且金额相近，有对敲可能 (A盈利:{pos_a_profit:.2f}, B盈利:{pos_b_profit:.2f})"
            elif similarity > 0.5:  # 盈利金额较相近
                adjusted_score = base_score * 0.6  # 中等权重
                explanation = f"双盈且金额较相近，中等对敲可能 (A盈利:{pos_a_profit:.2f}, B盈利:{pos_b_profit:.2f})"
            else:  # 盈利差距较大
                adjusted_score = base_score * 0.3  # 低权重
                explanation = f"双盈但差距较大，对敲可能性较低 (A盈利:{pos_a_profit:.2f}, B盈利:{pos_b_profit:.2f})"
        else:
            adjusted_score = base_score * 0.3
            explanation = f"双盈情况 (A盈利:{pos_a_profit:.2f}, B盈利:{pos_b_profit:.2f})"

    elif pos_a_profit < 0 and pos_b_profit < 0:
        # 双亏情况：差距越小，对敲可能性越高
        loss_diff = abs(pos_a_profit - pos_b_profit)
        avg_loss = abs((pos_a_profit + pos_b_profit) / 2)
        if avg_loss > 0:
            similarity = 1.0 - (loss_diff / avg_loss)  # 相似度
            if similarity > 0.8:  # 亏损金额非常相近
                adjusted_score = base_score * 0.9  # 高权重，可能是对敲
                explanation = f"双亏且金额相近，有对敲可能 (A亏损:{pos_a_profit:.2f}, B亏损:{pos_b_profit:.2f})"
            elif similarity > 0.5:  # 亏损金额较相近
                adjusted_score = base_score * 0.6  # 中等权重
                explanation = f"双亏且金额较相近，中等对敲可能 (A亏损:{pos_a_profit:.2f}, B亏损:{pos_b_profit:.2f})"
            else:  # 亏损差距较大
                adjusted_score = base_score * 0.3  # 低权重
                explanation = f"双亏但差距较大，对敲可能性较低 (A亏损:{pos_a_profit:.2f}, B亏损:{pos_b_profit:.2f})"
        else:
            adjusted_score = base_score * 0.3
            explanation = f"双亏情况 (A亏损:{pos_a_profit:.2f}, B亏损:{pos_b_profit:.2f})"

    else:
        # 一盈一亏情况：最符合对敲特征
        if abs(total_profit) < min(abs(pos_a_profit), abs(pos_b_profit)) * 0.1:
            # 盈亏几乎完全抵消（净盈亏<较小金额的10%）
            adjusted_score = min(base_score * 1.2, 1.0)  # 提高评分但不超过1.0
            explanation = f"一盈一亏且几乎完全抵消，对敲特征明显 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
        else:
            adjusted_score = base_score
            explanation = f"一盈一亏，符合对敲特征 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
    
    # 3. 如果提供了方向信息，进行方向相关性调整
    direction_bonus = 0.0
    if pos_a_direction and pos_b_direction:
        if pos_a_direction != pos_b_direction:  # 相反方向
            direction_bonus = 0.1  # 增加10%权重
        else:  # 相同方向
            direction_bonus = -0.1  # 减少10%权重
    
    # 4. 如果提供了交易量信息，进行交易量相关性调整
    volume_bonus = 0.0
    if pos_a_volume and pos_b_volume:
        volume_sum = pos_a_volume + pos_b_volume
        volume_diff = abs(pos_a_volume - pos_b_volume)
        volume_similarity = 1.0 - (volume_diff / volume_sum)
        if volume_similarity > 0.8:  # 交易量相近
            volume_bonus = 0.05  # 增加5%权重
    
    # 5. 最终评分
    final_score = min(adjusted_score + direction_bonus + volume_bonus, 1.0)
    final_score = max(final_score, 0.0)  # 确保不为负数
    
    # 6. 风险等级判定
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    return {
        'score': final_score,
        'base_score': base_score,
        'explanation': explanation,
        'risk_level': risk_level,
        'total_profit': total_profit,
        'profit_sum': profit_sum,
        'direction_bonus': direction_bonus,
        'volume_bonus': volume_bonus
    }


# 使用示例和测试
def demo_usage():
    """演示算法使用方法"""
    
    print("=== 对敲检测算法使用示例 ===\n")
    
    # 示例1：典型对敲
    result1 = calculate_hedge_score(100.0, -95.0, 1000, 1050, 'long', 'short')
    print("示例1 - 典型对敲:")
    print(f"评分: {result1['score']:.3f}")
    print(f"风险等级: {result1['risk_level']}")
    print(f"说明: {result1['explanation']}\n")
    
    # 示例2：双亏差距大
    result2 = calculate_hedge_score(-100.0, -10.0, 1000, 1050, 'long', 'short')
    print("示例2 - 双亏差距大:")
    print(f"评分: {result2['score']:.3f}")
    print(f"风险等级: {result2['risk_level']}")
    print(f"说明: {result2['explanation']}\n")
    
    # 示例3：双亏金额相近
    result3 = calculate_hedge_score(-100.0, -101.0, 1000, 1000, 'long', 'short')
    print("示例3 - 双亏金额相近:")
    print(f"评分: {result3['score']:.3f}")
    print(f"风险等级: {result3['risk_level']}")
    print(f"说明: {result3['explanation']}\n")
    
    # 示例4：完全对冲
    result4 = calculate_hedge_score(100.0, -100.0, 1000, 1000, 'long', 'short')
    print("示例4 - 完全对冲:")
    print(f"评分: {result4['score']:.3f}")
    print(f"风险等级: {result4['risk_level']}")
    print(f"说明: {result4['explanation']}\n")


if __name__ == "__main__":
    demo_usage()
