#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class UserDeduplicationAnalyzer:
    def __init__(self, data_path):
        """初始化用户去重统计分析器"""
        self.data_path = data_path
        self.df = None
        self.user_stats = None
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            self.df = pd.read_csv(self.data_path)
            print(f"原始数据加载成功，共{len(self.df):,}条记录")
            
            # 排除测试账号
            test_member_id = 'e5829ad101ce4c88a65848de29352a58'
            before_count = len(self.df)
            self.df = self.df[self.df['member_id'] != test_member_id]
            after_count = len(self.df)
            excluded_count = before_count - after_count
            print(f"已排除测试账号 {test_member_id}，删除 {excluded_count:,} 条记录")
            print(f"清理后数据共 {after_count:,} 条记录")
            
            # 数据预处理
            self.df['created_time'] = pd.to_datetime(self.df['created_time'])
            self.df['date'] = self.df['created_time'].dt.date
            self.df['hour'] = self.df['created_time'].dt.hour
            
            print("数据预处理完成")
            
        except Exception as e:
            print(f"数据加载失败: {e}")
    
    def analyze_user_deduplication(self):
        """用户去重统计分析"""
        print("\n=== 开始用户去重统计分析 ===")
        
        # 基础统计
        total_records = len(self.df)
        unique_users = self.df['member_id'].nunique()
        avg_records_per_user = total_records / unique_users
        
        print(f"\n📊 基础统计信息:")
        print(f"总记录数: {total_records:,}")
        print(f"唯一用户数: {unique_users:,}")
        print(f"平均每用户记录数: {avg_records_per_user:.2f}")
        
        # 用户活跃度统计
        user_activity = self.df.groupby('member_id').agg({
            'member_id': 'count',  # 操作次数
            'action_type': lambda x: x.nunique(),  # 操作类型数
            'via': lambda x: x.nunique(),  # 使用平台数
            'created_time': ['min', 'max'],  # 首次和最后操作时间
            'country': lambda x: x.iloc[0],  # 国家
            'city': lambda x: x.iloc[0]  # 城市
        }).round(2)
        
        # 重命名列
        user_activity.columns = ['操作次数', '操作类型数', '使用平台数', '首次操作时间', '最后操作时间', '国家', '城市']
        
        # 计算使用天数
        user_activity['使用天数'] = (user_activity['最后操作时间'] - user_activity['首次操作时间']).dt.days + 1
        
        self.user_stats = user_activity
        
        return user_activity
    
    def analyze_user_segments(self):
        """用户分层分析"""
        print("\n=== 用户活跃度分层分析 ===")
        
        # 按操作次数分层
        activity_bins = [0, 1, 5, 10, 20, 50, 100, float('inf')]
        activity_labels = ['1次', '2-5次', '6-10次', '11-20次', '21-50次', '51-100次', '100次以上']
        
        self.user_stats['活跃度分层'] = pd.cut(
            self.user_stats['操作次数'], 
            bins=activity_bins, 
            labels=activity_labels, 
            right=False
        )
        
        # 统计各分层
        segment_stats = self.user_stats['活跃度分层'].value_counts().sort_index()
        
        print("\n📈 用户活跃度分层统计:")
        for segment, count in segment_stats.items():
            percentage = count / len(self.user_stats) * 100
            print(f"{segment}: {count:,}人 ({percentage:.1f}%)")
        
        # 按使用天数分层
        duration_bins = [0, 1, 7, 30, 90, float('inf')]
        duration_labels = ['单日用户', '短期用户(2-7天)', '中期用户(8-30天)', '长期用户(31-90天)', '超长期用户(90天+)']
        
        self.user_stats['使用时长分层'] = pd.cut(
            self.user_stats['使用天数'], 
            bins=duration_bins, 
            labels=duration_labels, 
            right=False
        )
        
        duration_stats = self.user_stats['使用时长分层'].value_counts().sort_index()
        
        print("\n📅 用户使用时长分层统计:")
        for segment, count in duration_stats.items():
            percentage = count / len(self.user_stats) * 100
            print(f"{segment}: {count:,}人 ({percentage:.1f}%)")
        
        return segment_stats, duration_stats
    
    def analyze_user_behavior_patterns(self):
        """用户行为模式分析"""
        print("\n=== 用户行为模式分析 ===")
        
        # 操作类型多样性分析
        action_diversity = self.user_stats['操作类型数'].value_counts().sort_index()
        print("\n🎯 用户操作类型多样性:")
        for types, count in action_diversity.items():
            percentage = count / len(self.user_stats) * 100
            print(f"使用{types}种操作类型: {count:,}人 ({percentage:.1f}%)")
        
        # 平台使用多样性分析
        platform_diversity = self.user_stats['使用平台数'].value_counts().sort_index()
        print("\n📱 用户平台使用多样性:")
        for platforms, count in platform_diversity.items():
            percentage = count / len(self.user_stats) * 100
            print(f"使用{platforms}个平台: {count:,}人 ({percentage:.1f}%)")
        
        # 高价值用户特征分析
        high_value_users = self.user_stats[self.user_stats['操作次数'] >= 50]
        print(f"\n💎 高价值用户(50次以上操作)特征分析:")
        print(f"高价值用户数: {len(high_value_users):,}人")
        if len(high_value_users) > 0:
            print(f"平均操作次数: {high_value_users['操作次数'].mean():.1f}次")
            print(f"平均使用天数: {high_value_users['使用天数'].mean():.1f}天")
            print(f"平均操作类型数: {high_value_users['操作类型数'].mean():.1f}种")
            print(f"平均使用平台数: {high_value_users['使用平台数'].mean():.1f}个")
            
            # 高价值用户地域分布
            high_value_countries = high_value_users['国家'].value_counts().head(5)
            print(f"\n🌍 高价值用户主要国家:")
            for country, count in high_value_countries.items():
                percentage = count / len(high_value_users) * 100
                print(f"{country}: {count}人 ({percentage:.1f}%)")
        
        return action_diversity, platform_diversity, high_value_users
    
    def analyze_geographic_distribution(self):
        """地域分布去重分析"""
        print("\n=== 地域分布去重分析 ===")
        
        # 国家级别去重统计
        country_users = self.user_stats['国家'].value_counts()
        print(f"\n🌍 Top 15 国家用户分布:")
        for i, (country, count) in enumerate(country_users.head(15).items(), 1):
            percentage = count / len(self.user_stats) * 100
            print(f"{i:2d}. {country}: {count:,}人 ({percentage:.1f}%)")
        
        print(f"\n📊 地域覆盖统计:")
        print(f"覆盖国家数: {country_users.nunique():,}个")
        print(f"覆盖城市数: {self.user_stats['城市'].nunique():,}个")
        
        return country_users
    
    def create_visualizations(self):
        """创建可视化图表"""
        print("\n=== 生成可视化图表 ===")
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('用户去重统计分析图表', fontsize=16, fontweight='bold')
        
        # 1. 用户活跃度分层分布
        segment_stats = self.user_stats['活跃度分层'].value_counts().sort_index()
        axes[0, 0].pie(segment_stats.values, labels=segment_stats.index, autopct='%1.1f%%', startangle=90)
        axes[0, 0].set_title('用户活跃度分层分布', fontweight='bold')
        
        # 2. 用户使用时长分层分布
        duration_stats = self.user_stats['使用时长分层'].value_counts().sort_index()
        axes[0, 1].bar(range(len(duration_stats)), duration_stats.values)
        axes[0, 1].set_xticks(range(len(duration_stats)))
        axes[0, 1].set_xticklabels(duration_stats.index, rotation=45, ha='right')
        axes[0, 1].set_title('用户使用时长分层分布', fontweight='bold')
        axes[0, 1].set_ylabel('用户数')
        
        # 3. Top 10 国家用户分布
        top_countries = self.user_stats['国家'].value_counts().head(10)
        axes[1, 0].barh(range(len(top_countries)), top_countries.values)
        axes[1, 0].set_yticks(range(len(top_countries)))
        axes[1, 0].set_yticklabels(top_countries.index)
        axes[1, 0].set_title('Top 10 国家用户分布', fontweight='bold')
        axes[1, 0].set_xlabel('用户数')
        
        # 4. 操作次数分布直方图
        axes[1, 1].hist(self.user_stats['操作次数'], bins=50, alpha=0.7, edgecolor='black')
        axes[1, 1].set_title('用户操作次数分布', fontweight='bold')
        axes[1, 1].set_xlabel('操作次数')
        axes[1, 1].set_ylabel('用户数')
        axes[1, 1].set_xlim(0, 100)  # 限制x轴范围以便更好显示
        
        plt.tight_layout()
        plt.savefig('用户去重统计分析图表.png', dpi=300, bbox_inches='tight')
        print("图表已保存: 用户去重统计分析图表.png")
        
        return fig
    
    def export_to_excel(self):
        """导出详细数据到Excel"""
        print("\n=== 导出数据到Excel ===")
        
        with pd.ExcelWriter('用户去重统计分析数据.xlsx', engine='openpyxl') as writer:
            # 用户详细统计
            self.user_stats.to_excel(writer, sheet_name='用户详细统计', index=True)
            
            # 活跃度分层统计
            segment_stats = self.user_stats['活跃度分层'].value_counts().sort_index()
            segment_df = pd.DataFrame({
                '活跃度分层': segment_stats.index,
                '用户数': segment_stats.values,
                '占比(%)': (segment_stats.values / len(self.user_stats) * 100).round(1)
            })
            segment_df.to_excel(writer, sheet_name='活跃度分层统计', index=False)
            
            # 使用时长分层统计
            duration_stats = self.user_stats['使用时长分层'].value_counts().sort_index()
            duration_df = pd.DataFrame({
                '使用时长分层': duration_stats.index,
                '用户数': duration_stats.values,
                '占比(%)': (duration_stats.values / len(self.user_stats) * 100).round(1)
            })
            duration_df.to_excel(writer, sheet_name='使用时长分层统计', index=False)
            
            # 国家分布统计
            country_stats = self.user_stats['国家'].value_counts()
            country_df = pd.DataFrame({
                '国家': country_stats.index,
                '用户数': country_stats.values,
                '占比(%)': (country_stats.values / len(self.user_stats) * 100).round(2)
            })
            country_df.to_excel(writer, sheet_name='国家分布统计', index=False)
            
            # 高价值用户详情
            high_value_users = self.user_stats[self.user_stats['操作次数'] >= 50]
            if len(high_value_users) > 0:
                high_value_users.to_excel(writer, sheet_name='高价值用户详情', index=True)
        
        print("Excel文件已保存: 用户去重统计分析数据.xlsx")
    
    def generate_summary_report(self):
        """生成总结报告"""
        print("\n=== 生成总结报告 ===")
        
        # 基础统计
        total_records = len(self.df)
        unique_users = len(self.user_stats)
        avg_records_per_user = total_records / unique_users
        
        # 活跃度统计
        high_activity_users = len(self.user_stats[self.user_stats['操作次数'] >= 20])
        single_action_users = len(self.user_stats[self.user_stats['操作次数'] == 1])
        
        # 地域统计
        countries_count = self.user_stats['国家'].nunique()
        cities_count = self.user_stats['城市'].nunique()
        
        # 行为统计
        multi_platform_users = len(self.user_stats[self.user_stats['使用平台数'] > 1])
        multi_action_users = len(self.user_stats[self.user_stats['操作类型数'] > 1])
        
        report = f"""
# 用户去重统计分析总结报告

## 📊 核心指标
- **总记录数**: {total_records:,}
- **唯一用户数**: {unique_users:,}
- **人均操作次数**: {avg_records_per_user:.2f}
- **数据去重率**: {(1 - unique_users/total_records)*100:.1f}%

## 👥 用户活跃度分析
- **高活跃用户**(20次以上): {high_activity_users:,}人 ({high_activity_users/unique_users*100:.1f}%)
- **单次操作用户**: {single_action_users:,}人 ({single_action_users/unique_users*100:.1f}%)
- **多平台用户**: {multi_platform_users:,}人 ({multi_platform_users/unique_users*100:.1f}%)
- **多操作类型用户**: {multi_action_users:,}人 ({multi_action_users/unique_users*100:.1f}%)

## 🌍 地域覆盖
- **覆盖国家数**: {countries_count:,}个
- **覆盖城市数**: {cities_count:,}个
- **主要市场**: {self.user_stats['国家'].mode().iloc[0]}

## 💡 关键洞察
1. 平均每个用户产生{avg_records_per_user:.1f}条记录，显示用户粘性良好
2. {single_action_users/unique_users*100:.1f}%的用户只有单次操作，存在留存优化空间
3. {multi_platform_users/unique_users*100:.1f}%的用户使用多个平台，显示跨平台使用习惯
4. 覆盖{countries_count}个国家，国际化程度高

## 📈 优化建议
1. 重点关注单次操作用户的留存转化
2. 鼓励用户跨平台使用，提升用户价值
3. 基于地域特征制定本地化策略
4. 针对高活跃用户提供更多价值服务

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        with open('用户去重统计分析总结报告.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("总结报告已保存: 用户去重统计分析总结报告.md")
        print(report)
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print("开始用户去重统计完整分析...")
        
        # 执行各项分析
        self.analyze_user_deduplication()
        self.analyze_user_segments()
        self.analyze_user_behavior_patterns()
        self.analyze_geographic_distribution()
        
        # 生成可视化和报告
        self.create_visualizations()
        self.export_to_excel()
        self.generate_summary_report()
        
        print("\n✅ 用户去重统计分析完成！")
        print("生成的文件:")
        print("- 用户去重统计分析图表.png")
        print("- 用户去重统计分析数据.xlsx")
        print("- 用户去重统计分析总结报告.md")

if __name__ == "__main__":
    # 运行分析
    analyzer = UserDeduplicationAnalyzer('data/pf调用次数.csv')
    analyzer.run_complete_analysis()
