#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class UserDedupPlatformAnalyzer:
    def __init__(self, data_path):
        """初始化用户去重后平台操作分析器"""
        self.data_path = data_path
        self.df = None
        self.user_platform_stats = None
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            self.df = pd.read_csv(self.data_path)
            print(f"原始数据加载成功，共{len(self.df):,}条记录")
            
            # 排除测试账号
            test_member_id = 'e5829ad101ce4c88a65848de29352a58'
            before_count = len(self.df)
            self.df = self.df[self.df['member_id'] != test_member_id]
            after_count = len(self.df)
            excluded_count = before_count - after_count
            print(f"已排除测试账号 {test_member_id}，删除 {excluded_count:,} 条记录")
            print(f"清理后数据共 {after_count:,} 条记录")
            
            # 数据预处理
            self.df['created_time'] = pd.to_datetime(self.df['created_time'])
            self.df['date'] = self.df['created_time'].dt.date
            self.df['hour'] = self.df['created_time'].dt.hour
            
            print("数据预处理完成")
            
        except Exception as e:
            print(f"数据加载失败: {e}")
    
    def analyze_platform_user_dedup(self):
        """平台用户去重分析"""
        print("\n=== 平台用户去重分析 ===")
        
        # 各平台去重用户统计
        platform_users = self.df.groupby('via')['member_id'].nunique().sort_values(ascending=False)
        platform_records = self.df.groupby('via').size().sort_values(ascending=False)
        
        # 计算人均操作次数
        platform_avg = (platform_records / platform_users).round(2)
        
        # 合并统计
        platform_summary = pd.DataFrame({
            '去重用户数': platform_users,
            '总操作次数': platform_records,
            '人均操作次数': platform_avg,
            '用户占比(%)': (platform_users / platform_users.sum() * 100).round(1),
            '操作占比(%)': (platform_records / platform_records.sum() * 100).round(1)
        })
        
        print("\n📊 各平台去重用户统计:")
        print(platform_summary)
        
        return platform_summary
    
    def analyze_platform_action_dedup(self):
        """平台操作类型去重分析"""
        print("\n=== 平台操作类型去重分析 ===")
        
        # 平台-操作交叉分析(基于去重用户)
        platform_action_users = pd.crosstab(
            self.df['via'], 
            self.df['action_type'], 
            values=self.df['member_id'],
            aggfunc='nunique',
            margins=True
        )
        
        # 平台-操作交叉分析(基于操作次数)
        platform_action_counts = pd.crosstab(
            self.df['via'], 
            self.df['action_type'], 
            margins=True
        )
        
        print("\n📈 各平台各操作去重用户数:")
        print(platform_action_users)
        
        print("\n📊 各平台各操作总次数:")
        print(platform_action_counts)
        
        # 计算各平台操作类型占比
        platform_action_pct = pd.crosstab(
            self.df['via'], 
            self.df['action_type'], 
            normalize='index'
        ) * 100
        
        print("\n📋 各平台操作类型占比(%):")
        print(platform_action_pct.round(1))
        
        return platform_action_users, platform_action_counts, platform_action_pct
    
    def analyze_user_platform_behavior(self):
        """用户跨平台行为分析"""
        print("\n=== 用户跨平台行为分析 ===")
        
        # 每个用户使用的平台数量
        user_platforms = self.df.groupby('member_id')['via'].nunique()
        platform_diversity = user_platforms.value_counts().sort_index()
        
        print("\n🌐 用户平台使用多样性:")
        for platforms, count in platform_diversity.items():
            percentage = count / len(user_platforms) * 100
            print(f"使用{platforms}个平台: {count:,}人 ({percentage:.1f}%)")
        
        # 跨平台用户详细分析
        multi_platform_users = user_platforms[user_platforms > 1]
        print(f"\n📱 跨平台用户详细分析:")
        print(f"跨平台用户数: {len(multi_platform_users):,}人")
        print(f"跨平台用户占比: {len(multi_platform_users)/len(user_platforms)*100:.1f}%")
        
        if len(multi_platform_users) > 0:
            # 跨平台用户的平台组合分析
            multi_platform_combinations = self.df[self.df['member_id'].isin(multi_platform_users.index)].groupby('member_id')['via'].apply(lambda x: '+'.join(sorted(x.unique()))).value_counts()
            
            print(f"\n🔗 热门平台组合 (Top 10):")
            for i, (combo, count) in enumerate(multi_platform_combinations.head(10).items(), 1):
                percentage = count / len(multi_platform_users) * 100
                print(f"{i:2d}. {combo}: {count:,}人 ({percentage:.1f}%)")
        
        return platform_diversity, multi_platform_users
    
    def analyze_platform_user_value(self):
        """平台用户价值分析"""
        print("\n=== 平台用户价值分析 ===")
        
        # 计算每个用户在各平台的操作次数
        user_platform_activity = self.df.groupby(['member_id', 'via']).size().reset_index(name='操作次数')
        
        # 各平台用户价值分层
        platform_value_analysis = {}
        
        for platform in self.df['via'].unique():
            platform_data = user_platform_activity[user_platform_activity['via'] == platform]
            
            # 用户价值分层
            value_bins = [0, 1, 5, 10, 20, 50, float('inf')]
            value_labels = ['1次', '2-5次', '6-10次', '11-20次', '21-50次', '50次以上']
            
            platform_data['价值分层'] = pd.cut(
                platform_data['操作次数'], 
                bins=value_bins, 
                labels=value_labels, 
                right=False
            )
            
            value_distribution = platform_data['价值分层'].value_counts().sort_index()
            platform_value_analysis[platform] = value_distribution
            
            print(f"\n💎 {platform}平台用户价值分层:")
            for segment, count in value_distribution.items():
                percentage = count / len(platform_data) * 100
                print(f"  {segment}: {count:,}人 ({percentage:.1f}%)")
        
        return platform_value_analysis
    
    def analyze_platform_geographic_distribution(self):
        """平台地域分布分析"""
        print("\n=== 平台地域分布分析 ===")
        
        # 各平台主要国家分布
        for platform in ['Android', 'iOS', 'WEB', 'H5']:
            if platform in self.df['via'].values:
                platform_countries = self.df[self.df['via'] == platform]['country'].value_counts().head(10)
                platform_users = self.df[self.df['via'] == platform]['member_id'].nunique()
                
                print(f"\n🌍 {platform}平台 Top 10 国家分布 (去重用户):")
                for i, (country, count) in enumerate(platform_countries.items(), 1):
                    # 这里count是记录数，需要计算去重用户数
                    country_users = self.df[(self.df['via'] == platform) & (self.df['country'] == country)]['member_id'].nunique()
                    percentage = country_users / platform_users * 100
                    print(f"  {i:2d}. {country}: {country_users:,}人 ({percentage:.1f}%)")
    
    def analyze_platform_time_patterns(self):
        """平台时间使用模式分析"""
        print("\n=== 平台时间使用模式分析 ===")
        
        # 各平台用户活跃时间分布
        platform_hourly = self.df.groupby(['via', 'hour'])['member_id'].nunique().unstack(fill_value=0)
        
        print("\n⏰ 各平台用户活跃时间分布 (去重用户数):")
        print("平台\\时段", end="")
        for hour in range(0, 24, 3):
            print(f"\t{hour:02d}-{hour+2:02d}h", end="")
        print()
        
        for platform in platform_hourly.index:
            print(f"{platform}", end="")
            for hour in range(0, 24, 3):
                avg_users = platform_hourly.loc[platform, hour:hour+2].mean()
                print(f"\t{avg_users:.0f}", end="")
            print()
        
        # 各平台最活跃时段
        print(f"\n🕐 各平台最活跃时段:")
        for platform in platform_hourly.index:
            peak_hour = platform_hourly.loc[platform].idxmax()
            peak_users = platform_hourly.loc[platform, peak_hour]
            print(f"  {platform}: {peak_hour:02d}:00 ({peak_users:,}人)")
    
    def create_comprehensive_visualizations(self):
        """创建综合可视化图表"""
        print("\n=== 生成综合可视化图表 ===")
        
        # 创建2x3的图表布局
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('用户去重后平台操作详细分析图表', fontsize=16, fontweight='bold')
        
        # 1. 各平台去重用户数分布
        platform_users = self.df.groupby('via')['member_id'].nunique().sort_values(ascending=False)
        axes[0, 0].bar(platform_users.index, platform_users.values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        axes[0, 0].set_title('各平台去重用户数分布', fontweight='bold')
        axes[0, 0].set_ylabel('用户数')
        for i, v in enumerate(platform_users.values):
            axes[0, 0].text(i, v + 500, f'{v:,}', ha='center', va='bottom')
        
        # 2. 各平台人均操作次数
        platform_records = self.df.groupby('via').size()
        platform_avg = (platform_records / platform_users).sort_values(ascending=False)
        axes[0, 1].bar(platform_avg.index, platform_avg.values, color=['#FFD93D', '#6BCF7F', '#4D96FF', '#9B59B6'])
        axes[0, 1].set_title('各平台人均操作次数', fontweight='bold')
        axes[0, 1].set_ylabel('人均操作次数')
        for i, v in enumerate(platform_avg.values):
            axes[0, 1].text(i, v + 0.1, f'{v:.1f}', ha='center', va='bottom')
        
        # 3. 用户平台使用多样性
        user_platforms = self.df.groupby('member_id')['via'].nunique()
        platform_diversity = user_platforms.value_counts().sort_index()
        axes[0, 2].pie(platform_diversity.values, labels=[f'{i}个平台' for i in platform_diversity.index], 
                       autopct='%1.1f%%', startangle=90)
        axes[0, 2].set_title('用户平台使用多样性', fontweight='bold')
        
        # 4. 各平台操作类型分布(堆叠柱状图)
        platform_action_counts = pd.crosstab(self.df['via'], self.df['action_type'])
        platform_action_counts.plot(kind='bar', stacked=True, ax=axes[1, 0], 
                                   color=['#FF9999', '#66B2FF', '#99FF99', '#FFCC99'])
        axes[1, 0].set_title('各平台操作类型分布', fontweight='bold')
        axes[1, 0].set_ylabel('操作次数')
        axes[1, 0].legend(title='操作类型', bbox_to_anchor=(1.05, 1), loc='upper left')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 5. Top 10 国家用户分布
        top_countries = self.df['country'].value_counts().head(10)
        country_users = []
        for country in top_countries.index:
            users = self.df[self.df['country'] == country]['member_id'].nunique()
            country_users.append(users)
        
        axes[1, 1].barh(range(len(top_countries)), country_users)
        axes[1, 1].set_yticks(range(len(top_countries)))
        axes[1, 1].set_yticklabels(top_countries.index)
        axes[1, 1].set_title('Top 10 国家去重用户分布', fontweight='bold')
        axes[1, 1].set_xlabel('用户数')
        
        # 6. 各平台用户价值分层
        value_data = []
        platforms = ['Android', 'iOS', 'WEB', 'H5']
        value_segments = ['1次', '2-5次', '6-10次', '11-20次', '21-50次', '50次以上']
        
        for platform in platforms:
            if platform in self.df['via'].values:
                platform_data = self.df[self.df['via'] == platform]
                user_activity = platform_data.groupby('member_id').size()
                
                value_bins = [0, 1, 5, 10, 20, 50, float('inf')]
                value_labels = ['1次', '2-5次', '6-10次', '11-20次', '21-50次', '50次以上']
                
                user_segments = pd.cut(user_activity, bins=value_bins, labels=value_labels, right=False)
                segment_counts = user_segments.value_counts().reindex(value_segments, fill_value=0)
                value_data.append(segment_counts.values)
        
        # 创建堆叠条形图
        bottom = np.zeros(len(platforms))
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#F7DC6F', '#BB8FCE']
        
        for i, segment in enumerate(value_segments):
            values = [row[i] for row in value_data]
            axes[1, 2].bar(platforms, values, bottom=bottom, label=segment, color=colors[i])
            bottom += values
        
        axes[1, 2].set_title('各平台用户价值分层', fontweight='bold')
        axes[1, 2].set_ylabel('用户数')
        axes[1, 2].legend(title='价值分层', bbox_to_anchor=(1.05, 1), loc='upper left')
        
        plt.tight_layout()
        plt.savefig('用户去重后平台操作详细分析图表.png', dpi=300, bbox_inches='tight')
        print("图表已保存: 用户去重后平台操作详细分析图表.png")
        
        return fig
    
    def export_detailed_excel(self):
        """导出详细数据到Excel"""
        print("\n=== 导出详细数据到Excel ===")
        
        with pd.ExcelWriter('用户去重后平台操作详细数据.xlsx', engine='openpyxl') as writer:
            
            # 1. 平台用户去重统计
            platform_users = self.df.groupby('via')['member_id'].nunique().sort_values(ascending=False)
            platform_records = self.df.groupby('via').size().sort_values(ascending=False)
            platform_avg = (platform_records / platform_users).round(2)
            
            platform_summary = pd.DataFrame({
                '去重用户数': platform_users,
                '总操作次数': platform_records,
                '人均操作次数': platform_avg,
                '用户占比(%)': (platform_users / platform_users.sum() * 100).round(1),
                '操作占比(%)': (platform_records / platform_records.sum() * 100).round(1)
            })
            platform_summary.to_excel(writer, sheet_name='平台用户去重统计', index=True)
            
            # 2. 平台操作交叉分析(去重用户)
            platform_action_users = pd.crosstab(
                self.df['via'], 
                self.df['action_type'], 
                values=self.df['member_id'],
                aggfunc='nunique',
                margins=True
            )
            platform_action_users.to_excel(writer, sheet_name='平台操作去重用户交叉表', index=True)
            
            # 3. 平台操作交叉分析(操作次数)
            platform_action_counts = pd.crosstab(self.df['via'], self.df['action_type'], margins=True)
            platform_action_counts.to_excel(writer, sheet_name='平台操作次数交叉表', index=True)
            
            # 4. 用户跨平台行为分析
            user_platforms = self.df.groupby('member_id')['via'].nunique()
            platform_diversity = user_platforms.value_counts().sort_index()
            diversity_df = pd.DataFrame({
                '使用平台数': platform_diversity.index,
                '用户数': platform_diversity.values,
                '占比(%)': (platform_diversity.values / len(user_platforms) * 100).round(1)
            })
            diversity_df.to_excel(writer, sheet_name='用户跨平台行为分析', index=False)
            
            # 5. 各平台国家分布
            platform_country_data = []
            for platform in ['Android', 'iOS', 'WEB', 'H5']:
                if platform in self.df['via'].values:
                    platform_data = self.df[self.df['via'] == platform]
                    country_stats = platform_data.groupby('country')['member_id'].nunique().sort_values(ascending=False).head(15)
                    
                    for country, users in country_stats.items():
                        platform_country_data.append({
                            '平台': platform,
                            '国家': country,
                            '去重用户数': users,
                            '占该平台比例(%)': round(users / platform_data['member_id'].nunique() * 100, 1)
                        })
            
            platform_country_df = pd.DataFrame(platform_country_data)
            platform_country_df.to_excel(writer, sheet_name='各平台国家分布', index=False)
            
            # 6. 用户详细信息
            user_detail = self.df.groupby('member_id').agg({
                'via': lambda x: '+'.join(sorted(x.unique())),  # 使用的平台
                'action_type': lambda x: '+'.join(sorted(x.unique())),  # 使用的操作类型
                'country': 'first',  # 国家
                'city': 'first',  # 城市
                'created_time': ['min', 'max', 'count'],  # 首次、最后操作时间和总次数
            })
            
            user_detail.columns = ['使用平台', '操作类型', '国家', '城市', '首次操作时间', '最后操作时间', '总操作次数']
            user_detail['使用天数'] = (pd.to_datetime(user_detail['最后操作时间']) - pd.to_datetime(user_detail['首次操作时间'])).dt.days + 1
            user_detail['使用平台数'] = user_detail['使用平台'].str.count('\+') + 1
            user_detail['操作类型数'] = user_detail['操作类型'].str.count('\+') + 1
            
            # 只保存前10000个用户的详细信息（文件大小考虑）
            user_detail.head(10000).to_excel(writer, sheet_name='用户详细信息(前10000)', index=True)
        
        print("Excel文件已保存: 用户去重后平台操作详细数据.xlsx")
    
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print("\n=== 生成综合分析报告 ===")
        
        # 基础统计
        total_records = len(self.df)
        unique_users = self.df['member_id'].nunique()
        unique_platforms = self.df['via'].nunique()
        unique_countries = self.df['country'].nunique()
        
        # 平台统计
        platform_users = self.df.groupby('via')['member_id'].nunique().sort_values(ascending=False)
        platform_records = self.df.groupby('via').size().sort_values(ascending=False)
        
        # 跨平台用户统计
        user_platforms = self.df.groupby('member_id')['via'].nunique()
        multi_platform_users = len(user_platforms[user_platforms > 1])
        
        # 高价值用户统计
        user_activity = self.df.groupby('member_id').size()
        high_value_users = len(user_activity[user_activity >= 20])
        
        report = f"""
# 用户去重后平台操作详细分析报告

## 📊 核心数据概览

### 🎯 基础统计
- **总操作记录**: {total_records:,}条
- **去重用户数**: {unique_users:,}人
- **覆盖平台数**: {unique_platforms}个
- **覆盖国家数**: {unique_countries}个
- **平均每用户操作**: {total_records/unique_users:.2f}次

### 📱 平台用户分布
"""
        
        for platform, users in platform_users.items():
            records = platform_records[platform]
            avg_ops = records / users
            user_pct = users / unique_users * 100
            record_pct = records / total_records * 100
            report += f"- **{platform}平台**: {users:,}人 ({user_pct:.1f}%) | {records:,}次操作 ({record_pct:.1f}%) | 人均{avg_ops:.1f}次\n"
        
        report += f"""

### 🌐 用户行为特征
- **单平台用户**: {unique_users - multi_platform_users:,}人 ({(unique_users - multi_platform_users)/unique_users*100:.1f}%)
- **跨平台用户**: {multi_platform_users:,}人 ({multi_platform_users/unique_users*100:.1f}%)
- **高价值用户**(20次以上): {high_value_users:,}人 ({high_value_users/unique_users*100:.1f}%)

## 🔍 平台操作详细分析

### 📊 各平台操作类型分布
"""
        
        # 添加各平台操作类型分析
        platform_action_pct = pd.crosstab(self.df['via'], self.df['action_type'], normalize='index') * 100
        
        for platform in platform_action_pct.index:
            report += f"\n#### {platform}平台操作分布:\n"
            for action, pct in platform_action_pct.loc[platform].items():
                if pct > 0:
                    action_users = len(self.df[(self.df['via'] == platform) & (self.df['action_type'] == action)]['member_id'].unique())
                    report += f"- {action}: {pct:.1f}% ({action_users:,}人使用)\n"
        
        report += f"""

## 🌍 地域分布分析

### 🏆 Top 10 国家用户分布
"""
        
        top_countries = self.df['country'].value_counts().head(10)
        for i, (country, records) in enumerate(top_countries.items(), 1):
            users = self.df[self.df['country'] == country]['member_id'].nunique()
            pct = users / unique_users * 100
            avg_ops = records / users
            report += f"{i:2d}. **{country}**: {users:,}人 ({pct:.1f}%) | 人均{avg_ops:.1f}次操作\n"
        
        report += f"""

## 💡 关键洞察

### 🎯 平台特征分析
1. **{platform_users.index[0]}平台**是最大用户平台，拥有{platform_users.iloc[0]:,}人({platform_users.iloc[0]/unique_users*100:.1f}%)
2. **跨平台用户**占{multi_platform_users/unique_users*100:.1f}%，显示良好的产品生态粘性
3. **高价值用户**占{high_value_users/unique_users*100:.1f}%，需要重点维护和深度运营

### 📈 优化建议
1. **平台协同优化**: 提升跨平台用户体验，增加用户粘性
2. **用户价值提升**: 针对轻度用户制定激活策略
3. **地域化运营**: 基于主要市场特征制定本地化策略
4. **功能引导优化**: 基于各平台操作特征优化功能布局

## 📊 数据质量说明
- 本报告基于去重后的{unique_users:,}名真实用户数据
- 已排除测试账号数据，确保分析结果准确性
- 数据时间范围: {self.df['created_time'].min().strftime('%Y-%m-%d')} 至 {self.df['created_time'].max().strftime('%Y-%m-%d')}

---

*📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*  
*📊 数据基础: {unique_users:,}名去重用户，{total_records:,}条操作记录*  
*🎯 分析维度: 平台×用户×操作×地域×时间*
        """
        
        with open('用户去重后平台操作详细分析报告.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("详细分析报告已保存: 用户去重后平台操作详细分析报告.md")
        print(report)
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print("开始用户去重后平台操作详细分析...")
        
        # 执行各项分析
        self.analyze_platform_user_dedup()
        self.analyze_platform_action_dedup()
        self.analyze_user_platform_behavior()
        self.analyze_platform_user_value()
        self.analyze_platform_geographic_distribution()
        self.analyze_platform_time_patterns()
        
        # 生成可视化和报告
        self.create_comprehensive_visualizations()
        self.export_detailed_excel()
        self.generate_comprehensive_report()
        
        print("\n✅ 用户去重后平台操作详细分析完成！")
        print("生成的文件:")
        print("- 用户去重后平台操作详细分析图表.png")
        print("- 用户去重后平台操作详细数据.xlsx")
        print("- 用户去重后平台操作详细分析报告.md")

if __name__ == "__main__":
    # 运行分析
    analyzer = UserDedupPlatformAnalyzer('data/pf调用次数.csv')
    analyzer.run_complete_analysis()
