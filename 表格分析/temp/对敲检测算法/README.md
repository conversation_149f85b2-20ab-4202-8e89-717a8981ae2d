# 对敲检测算法

## 🎯 功能概述

对敲检测算法模块包含多种对敲行为检测算法，用于识别用户交易中的异常对敲行为，防范交易风险。

## 🔧 算法分类

### 核心算法 ⭐
- **核心对敲检测算法_最终版.py** - 最终优化版本，推荐使用
- **核心对敲检测算法.py** - 基础核心算法
- **完整对敲检测算法.py** - 完整功能版本
- **正确的对敲检测算法.py** - 修正版算法

### 改进算法 🚀
- **improved_hedge_detection.py** - 改进的对敲检测算法
- **优化对敲检测算法.py** - 性能优化版本
- **改进的对敲检测公式.py** - 公式改进版本
- **融合改进的对敲检测算法.py** - 多算法融合版本

### 专项算法 🎲
- **三要素对敲检测算法.py** - 基于三要素的检测方法
- **双因子对敲检测算法.py** - 双因子检测算法
- **相对波动率加接近0的对敲检测.py** - 基于波动率的检测
- **同边异边分流对敲检测算法.py** - 分流检测算法
- **取负值转换的对敲检测算法.py** - 负值转换检测

### 规模化算法 📊
- **处理不同规模仓位的对敲检测.py** - 多规模仓位处理
- **考虑交易规模的对敲检测.py** - 交易规模考量
- **大规模对敲检测测试.py** - 大规模数据测试
- **基于开单金额的对敲检测测试.py** - 开单金额检测

### 简化算法 ⚡
- **简化对敲检测算法.py** - 简化版本，快速检测
- **简化的相对波动率对敲检测.py** - 简化波动率检测

### 综合算法 🔄
- **综合考虑的对敲检测算法.py** - 多因素综合考虑
- **分情况全面对敲检测算法.py** - 分情况处理
- **完整的比例控制对敲检测算法.py** - 比例控制版本

## 🧪 测试文件

### 基础测试
- **对敲算法测试.py** - 基础功能测试
- **双因子计算测试.py** - 双因子算法测试

### 对比测试
- **参数调整对比测试.py** - 参数调整效果对比
- **优化后对敲检测测试.py** - 优化效果测试
- **综合优化对敲检测测试.py** - 综合优化测试

## 🚀 使用方法

### 推荐使用（最终版本）
```python
from 核心对敲检测算法_最终版 import calculate_hedge_score

# 基础对敲检测
score = calculate_hedge_score(profit_a=100, profit_b=-95)
print(f"对敲风险评分: {score}")

# 考虑交易规模
score = calculate_hedge_score(profit_a=100, profit_b=-95, expected_scale=1000)
print(f"对敲风险评分: {score}")
```

### 专项算法使用
```python
# 三要素检测
from 三要素对敲检测算法 import detect_hedge_three_factors
result = detect_hedge_three_factors(profit, direction, volume)

# 双因子检测
from 双因子对敲检测算法 import dual_factor_detection
result = dual_factor_detection(factor1, factor2)

# 波动率检测
from 相对波动率加接近0的对敲检测 import volatility_hedge_detection
result = volatility_hedge_detection(price_data, threshold=0.01)
```

## 📊 算法特点

### 核心算法特点
- **高准确率**: 综合多种检测指标
- **低误报率**: 优化的阈值设置
- **实时性**: 支持实时数据处理
- **可扩展**: 支持自定义参数

### 检测维度
1. **盈亏相关性** - 分析两个仓位的盈亏关系
2. **交易方向** - 检测相反方向的交易
3. **交易量** - 分析交易量的相似性
4. **时间相关性** - 检测交易时间的关联性
5. **价格波动** - 分析价格波动的异常性

### 评分机制
- **0.0-0.3**: 低风险，正常交易
- **0.3-0.6**: 中等风险，需要关注
- **0.6-0.8**: 高风险，疑似对敲
- **0.8-1.0**: 极高风险，强烈疑似对敲

## ⚙️ 参数配置

### 核心参数
- **profit_threshold**: 盈亏阈值
- **correlation_threshold**: 相关性阈值
- **time_window**: 时间窗口
- **volume_ratio**: 交易量比例

### 优化参数
- **weight_profit**: 盈亏权重
- **weight_direction**: 方向权重
- **weight_volume**: 交易量权重
- **weight_time**: 时间权重

## 🔍 检测流程

### 1. 数据预处理
- 数据清洗和格式化
- 异常值处理
- 缺失值填充

### 2. 特征提取
- 盈亏特征计算
- 方向特征提取
- 时间特征分析
- 交易量特征

### 3. 相关性分析
- 盈亏相关性计算
- 时间相关性分析
- 交易量相关性

### 4. 风险评分
- 多维度评分计算
- 权重分配
- 最终评分生成

### 5. 结果输出
- 风险等级判定
- 详细分析报告
- 可视化展示

## 📈 性能指标

### 准确性指标
- **准确率**: 正确识别率
- **召回率**: 对敲检出率
- **精确率**: 误报控制率
- **F1分数**: 综合评价指标

### 性能指标
- **处理速度**: 每秒处理交易数
- **内存使用**: 内存占用情况
- **CPU使用**: CPU占用率

## 🛠️ 维护说明

### 算法更新
- 定期评估算法效果
- 根据新的对敲模式调整参数
- 增加新的检测维度

### 性能优化
- 算法执行效率优化
- 内存使用优化
- 并行处理支持

### 测试验证
- 定期进行回测验证
- 新算法A/B测试
- 效果对比分析
