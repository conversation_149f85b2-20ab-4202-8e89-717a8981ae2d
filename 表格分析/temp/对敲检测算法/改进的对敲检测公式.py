#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的对敲检测公式
解决大金额波动但仍是对敲行为的问题
"""

import math

def calculate_hedge_score_improved_formula(pos_a_profit, pos_b_profit):
    """
    改进的对敲检测公式
    
    核心思想：
    1. 一盈一亏：总盈亏越接近0越好（原逻辑不变）
    2. 双盈/双亏：考虑相对波动率，而不是绝对金额
    
    Args:
        pos_a_profit: 仓位A的实际盈亏
        pos_b_profit: 仓位B的实际盈亏
    
    Returns:
        dict: 包含评分和详细信息
    """
    
    # === 基础计算 ===
    total_profit = pos_a_profit + pos_b_profit
    profit_sum = abs(pos_a_profit) + abs(pos_b_profit)
    
    if profit_sum == 0:
        return {
            'score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'case_type': '无盈亏'
        }
    
    # 基础对敲评分
    base_score = 1.0 - abs(total_profit) / profit_sum
    
    # === 改进的逻辑 ===
    
    if pos_a_profit > 0 and pos_b_profit > 0:
        # 双盈情况：计算相对波动率
        case_type = "双盈"
        
        # 方法1：相对标准差法
        mean_profit = (pos_a_profit + pos_b_profit) / 2
        if mean_profit > 0:
            # 计算变异系数（标准差/均值）
            std_dev = math.sqrt(((pos_a_profit - mean_profit)**2 + (pos_b_profit - mean_profit)**2) / 2)
            coefficient_of_variation = std_dev / mean_profit
            
            # 变异系数越小，说明两个值越接近，对敲可能性越高
            if coefficient_of_variation <= 0.05:  # 5%以内的波动
                adjustment_factor = 0.9
                explanation = f"双盈且相对波动很小({coefficient_of_variation:.1%})，有对敲可能"
            elif coefficient_of_variation <= 0.15:  # 15%以内的波动
                adjustment_factor = 0.6
                explanation = f"双盈且相对波动较小({coefficient_of_variation:.1%})，中等对敲可能"
            else:  # 波动较大
                adjustment_factor = 0.3
                explanation = f"双盈且相对波动较大({coefficient_of_variation:.1%})，对敲可能性较低"
        else:
            adjustment_factor = 0.3
            explanation = "双盈情况"
            
    elif pos_a_profit < 0 and pos_b_profit < 0:
        # 双亏情况：计算相对波动率
        case_type = "双亏"
        
        # 对于负数，取绝对值计算
        abs_a = abs(pos_a_profit)
        abs_b = abs(pos_b_profit)
        mean_loss = (abs_a + abs_b) / 2
        
        if mean_loss > 0:
            # 计算变异系数
            std_dev = math.sqrt(((abs_a - mean_loss)**2 + (abs_b - mean_loss)**2) / 2)
            coefficient_of_variation = std_dev / mean_loss
            
            # 变异系数越小，说明两个亏损越接近，对敲可能性越高
            if coefficient_of_variation <= 0.05:  # 5%以内的波动
                adjustment_factor = 0.9
                explanation = f"双亏且相对波动很小({coefficient_of_variation:.1%})，有对敲可能"
            elif coefficient_of_variation <= 0.15:  # 15%以内的波动
                adjustment_factor = 0.6
                explanation = f"双亏且相对波动较小({coefficient_of_variation:.1%})，中等对敲可能"
            else:  # 波动较大
                adjustment_factor = 0.3
                explanation = f"双亏且相对波动较大({coefficient_of_variation:.1%})，对敲可能性较低"
        else:
            adjustment_factor = 0.3
            explanation = "双亏情况"
            
    else:
        # 一盈一亏情况：保持原逻辑
        case_type = "一盈一亏"
        
        if abs(total_profit) < min(abs(pos_a_profit), abs(pos_b_profit)) * 0.1:
            adjustment_factor = 1.2
            explanation = f"一盈一亏且几乎完全抵消，对敲特征明显"
        else:
            adjustment_factor = 1.0
            explanation = f"一盈一亏，符合对敲特征"
    
    # 最终评分
    final_score = min(base_score * adjustment_factor, 1.0)
    
    # 风险等级
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    return {
        'score': final_score,
        'base_score': base_score,
        'adjustment_factor': adjustment_factor,
        'explanation': explanation,
        'case_type': case_type,
        'risk_level': risk_level,
        'total_profit': total_profit,
        'profit_sum': profit_sum
    }


def demo_improved_formula():
    """演示改进公式的效果"""
    
    print("=== 改进的对敲检测公式演示 ===\n")
    print("核心改进：使用相对波动率而不是绝对金额判断\n")
    
    test_cases = [
        # 一盈一亏（保持不变）
        (100.0, -95.0, "典型对敲"),
        (1000.0, -950.0, "大金额典型对敲"),
        
        # 双盈情况：测试相对波动
        (100.0, 102.0, "双盈小波动(2%)"),
        (100.0, 110.0, "双盈中等波动(10%)"),
        (100.0, 150.0, "双盈大波动(50%)"),
        
        # 大金额双盈：测试相对波动
        (10000.0, 10200.0, "大金额双盈小波动(2%)"),
        (10000.0, 11000.0, "大金额双盈中等波动(10%)"),
        (10000.0, 15000.0, "大金额双盈大波动(50%)"),
        
        # 双亏情况：测试相对波动
        (-100.0, -102.0, "双亏小波动(2%)"),
        (-100.0, -110.0, "双亏中等波动(10%)"),
        (-100.0, -150.0, "双亏大波动(50%)"),
        
        # 大金额双亏：测试相对波动
        (-10000.0, -10200.0, "大金额双亏小波动(2%)"),
        (-10000.0, -11000.0, "大金额双亏中等波动(10%)"),
        (-10000.0, -15000.0, "大金额双亏大波动(50%)"),
    ]
    
    for pos_a, pos_b, desc in test_cases:
        result = calculate_hedge_score_improved_formula(pos_a, pos_b)
        
        print(f"{desc}: A={pos_a:.0f}, B={pos_b:.0f}")
        print(f"  基础评分: {result['base_score']:.3f}")
        print(f"  调整因子: {result['adjustment_factor']:.1f}")
        print(f"  最终评分: {result['score']:.3f}")
        print(f"  风险等级: {result['risk_level']}")
        print(f"  说明: {result['explanation']}")
        print()


if __name__ == "__main__":
    demo_improved_formula()
