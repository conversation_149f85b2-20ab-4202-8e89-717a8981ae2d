#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的对敲检测算法
核心逻辑：两个仓位的盈亏都越接近0，对敲可能性越高
"""

def calculate_hedge_score_correct(pos_a_profit, pos_b_profit, pos_a_volume=None, pos_b_volume=None, 
                                 pos_a_direction=None, pos_b_direction=None):
    """
    正确的对敲检测算法
    
    核心逻辑：
    1. 一盈一亏：经典对敲特征，总盈亏越接近0越好
    2. 双盈或双亏：两个仓位的盈亏都越接近0，对敲可能性越高
    
    Args:
        pos_a_profit: 仓位A的实际盈亏
        pos_b_profit: 仓位B的实际盈亏  
        pos_a_volume: 仓位A的交易量（可选）
        pos_b_volume: 仓位B的交易量（可选）
        pos_a_direction: 仓位A的方向 'long'/'short'（可选）
        pos_b_direction: 仓位B的方向 'long'/'short'（可选）
    
    Returns:
        dict: 包含评分和详细信息的字典
    """
    
    # 1. 基础盈利相关性计算
    total_profit = pos_a_profit + pos_b_profit
    profit_sum = abs(pos_a_profit) + abs(pos_b_profit)
    
    if profit_sum == 0:
        return {
            'score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'risk_level': '无法判断'
        }
    
    # 基础对冲评分：总盈亏越接近0，评分越高
    base_score = 1.0 - abs(total_profit) / profit_sum
    
    # 2. 根据盈亏组合情况调整评分
    if pos_a_profit > 0 and pos_b_profit > 0:
        # 双盈情况：两个盈利都越接近0，对敲可能性越高
        max_profit = max(abs(pos_a_profit), abs(pos_b_profit))
        
        # 定义接近0的阈值（可根据实际业务调整）
        if max_profit <= 10:  # 两个盈利都很小（接近0）
            adjusted_score = base_score * 0.9  # 高权重，可能是对敲
            explanation = f"双盈且都接近0，有对敲可能 (A盈利:{pos_a_profit:.2f}, B盈利:{pos_b_profit:.2f})"
        elif max_profit <= 50:  # 盈利较小
            adjusted_score = base_score * 0.6  # 中等权重
            explanation = f"双盈且金额较小，中等对敲可能 (A盈利:{pos_a_profit:.2f}, B盈利:{pos_b_profit:.2f})"
        else:  # 盈利较大，不太可能是对敲
            adjusted_score = base_score * 0.3  # 低权重
            explanation = f"双盈且金额较大，对敲可能性较低 (A盈利:{pos_a_profit:.2f}, B盈利:{pos_b_profit:.2f})"
        
    elif pos_a_profit < 0 and pos_b_profit < 0:
        # 双亏情况：两个亏损都越接近0，对敲可能性越高
        max_loss = max(abs(pos_a_profit), abs(pos_b_profit))
        
        # 定义接近0的阈值（可根据实际业务调整）
        if max_loss <= 10:  # 两个亏损都很小（接近0）
            adjusted_score = base_score * 0.9  # 高权重，可能是对敲
            explanation = f"双亏且都接近0，有对敲可能 (A亏损:{pos_a_profit:.2f}, B亏损:{pos_b_profit:.2f})"
        elif max_loss <= 50:  # 亏损较小
            adjusted_score = base_score * 0.6  # 中等权重
            explanation = f"双亏且金额较小，中等对敲可能 (A亏损:{pos_a_profit:.2f}, B亏损:{pos_b_profit:.2f})"
        else:  # 亏损较大，不太可能是对敲
            adjusted_score = base_score * 0.3  # 低权重
            explanation = f"双亏且金额较大，对敲可能性较低 (A亏损:{pos_a_profit:.2f}, B亏损:{pos_b_profit:.2f})"
            
    else:
        # 一盈一亏情况：最符合对敲特征
        if abs(total_profit) < min(abs(pos_a_profit), abs(pos_b_profit)) * 0.1:
            # 盈亏几乎完全抵消（净盈亏<较小金额的10%）
            adjusted_score = min(base_score * 1.2, 1.0)  # 提高评分但不超过1.0
            explanation = f"一盈一亏且几乎完全抵消，对敲特征明显 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
        else:
            adjusted_score = base_score
            explanation = f"一盈一亏，符合对敲特征 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
    
    # 3. 如果提供了方向信息，进行方向相关性调整
    direction_bonus = 0.0
    if pos_a_direction and pos_b_direction:
        if pos_a_direction != pos_b_direction:  # 相反方向
            direction_bonus = 0.1  # 增加10%权重
        else:  # 相同方向
            direction_bonus = -0.05  # 减少5%权重
    
    # 4. 如果提供了交易量信息，进行交易量相关性调整
    volume_bonus = 0.0
    if pos_a_volume and pos_b_volume:
        volume_sum = pos_a_volume + pos_b_volume
        volume_diff = abs(pos_a_volume - pos_b_volume)
        volume_similarity = 1.0 - (volume_diff / volume_sum)
        if volume_similarity > 0.8:  # 交易量相近
            volume_bonus = 0.05  # 增加5%权重
    
    # 5. 最终评分
    final_score = min(adjusted_score + direction_bonus + volume_bonus, 1.0)
    final_score = max(final_score, 0.0)  # 确保不为负数
    
    # 6. 风险等级判定
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    return {
        'score': final_score,
        'base_score': base_score,
        'explanation': explanation,
        'risk_level': risk_level,
        'total_profit': total_profit,
        'profit_sum': profit_sum,
        'direction_bonus': direction_bonus,
        'volume_bonus': volume_bonus
    }


# 使用示例和测试
def demo_correct_usage():
    """演示正确算法的使用方法"""
    
    print("=== 正确的对敲检测算法测试 ===\n")
    
    # 示例1：典型对敲（一盈一亏）
    result1 = calculate_hedge_score_correct(100.0, -95.0, 1000, 1050, 'long', 'short')
    print("示例1 - 典型对敲（一盈一亏）:")
    print(f"评分: {result1['score']:.3f}")
    print(f"风险等级: {result1['risk_level']}")
    print(f"说明: {result1['explanation']}\n")
    
    # 示例2：双盈且都接近0
    result2 = calculate_hedge_score_correct(2.0, 3.0, 1000, 1050, 'long', 'short')
    print("示例2 - 双盈且都接近0:")
    print(f"评分: {result2['score']:.3f}")
    print(f"风险等级: {result2['risk_level']}")
    print(f"说明: {result2['explanation']}\n")
    
    # 示例3：双亏且都接近0
    result3 = calculate_hedge_score_correct(-2.0, -3.0, 1000, 1000, 'long', 'short')
    print("示例3 - 双亏且都接近0:")
    print(f"评分: {result3['score']:.3f}")
    print(f"风险等级: {result3['risk_level']}")
    print(f"说明: {result3['explanation']}\n")
    
    # 示例4：双盈但金额较大
    result4 = calculate_hedge_score_correct(100.0, 120.0, 1000, 1000, 'long', 'short')
    print("示例4 - 双盈但金额较大:")
    print(f"评分: {result4['score']:.3f}")
    print(f"风险等级: {result4['risk_level']}")
    print(f"说明: {result4['explanation']}\n")
    
    # 示例5：双亏但金额较大
    result5 = calculate_hedge_score_correct(-100.0, -120.0, 1000, 1000, 'long', 'short')
    print("示例5 - 双亏但金额较大:")
    print(f"评分: {result5['score']:.3f}")
    print(f"风险等级: {result5['risk_level']}")
    print(f"说明: {result5['explanation']}\n")


if __name__ == "__main__":
    demo_correct_usage()
