#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的对敲检测算法
解决双亏情况下的逻辑问题
"""

import math
from typing import Tuple, Optional
from dataclasses import dataclass

@dataclass
class Position:
    """仓位信息"""
    real_profit: float  # 实际盈亏
    volume: float      # 交易量
    direction: str     # 方向：'long' 或 'short'

def calculate_hedge_score_improved(pos_a: Position, pos_b: Position) -> Tuple[float, str]:
    """
    改进的对敲盈利相关性评分算法
    
    Args:
        pos_a: 仓位A
        pos_b: 仓位B
    
    Returns:
        Tuple[float, str]: (评分, 评分说明)
    """
    total_profit = pos_a.real_profit + pos_b.real_profit
    profit_sum = abs(pos_a.real_profit) + abs(pos_b.real_profit)
    
    # 如果两个仓位都没有盈亏，无法判断
    if profit_sum == 0:
        return 0.0, "两个仓位都无盈亏，无法判断对敲特征"
    
    # 基础对冲评分：总盈亏越接近0，评分越高
    base_hedge_score = 1.0 - abs(total_profit) / profit_sum
    
    # 根据盈亏情况进行调整
    pos_a_profit = pos_a.real_profit
    pos_b_profit = pos_b.real_profit
    
    if pos_a_profit > 0 and pos_b_profit > 0:
        # 双盈情况：不太可能是对敲，降低评分
        adjusted_score = base_hedge_score * 0.3
        explanation = f"双盈情况，对敲可能性较低 (A盈利:{pos_a_profit:.2f}, B盈利:{pos_b_profit:.2f})"
        
    elif pos_a_profit < 0 and pos_b_profit < 0:
        # 双亏情况：需要看亏损是否相近
        if base_hedge_score > 0.8:  # 亏损金额非常相近
            adjusted_score = base_hedge_score * 0.8  # 仍有对敲可能，但降低权重
            explanation = f"双亏但金额相近，有对敲可能 (A亏损:{pos_a_profit:.2f}, B亏损:{pos_b_profit:.2f})"
        else:  # 亏损差距较大
            adjusted_score = base_hedge_score * 0.2  # 大幅降低评分
            explanation = f"双亏且差距较大，对敲可能性很低 (A亏损:{pos_a_profit:.2f}, B亏损:{pos_b_profit:.2f})"
            
    else:
        # 一盈一亏情况：最符合对敲特征
        if abs(pos_a_profit + pos_b_profit) < min(abs(pos_a_profit), abs(pos_b_profit)) * 0.1:
            # 盈亏几乎完全抵消
            adjusted_score = min(base_hedge_score * 1.2, 1.0)  # 提高评分但不超过1.0
            explanation = f"一盈一亏且几乎完全抵消，对敲特征明显 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
        else:
            adjusted_score = base_hedge_score
            explanation = f"一盈一亏，符合对敲特征 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
    
    return min(adjusted_score, 1.0), explanation

def calculate_comprehensive_hedge_score(pos_a: Position, pos_b: Position) -> Tuple[float, dict]:
    """
    综合对敲评分，考虑多个维度
    
    Args:
        pos_a: 仓位A
        pos_b: 仓位B
    
    Returns:
        Tuple[float, dict]: (综合评分, 详细评分信息)
    """
    # 1. 盈利相关性评分
    profit_score, profit_explanation = calculate_hedge_score_improved(pos_a, pos_b)
    
    # 2. 方向相关性评分（对敲通常是相反方向）
    direction_score = 1.0 if pos_a.direction != pos_b.direction else 0.3
    
    # 3. 交易量相关性评分（对敲通常交易量相近）
    volume_sum = pos_a.volume + pos_b.volume
    if volume_sum > 0:
        volume_diff = abs(pos_a.volume - pos_b.volume)
        volume_score = 1.0 - (volume_diff / volume_sum)
    else:
        volume_score = 0.0
    
    # 综合评分（可以调整权重）
    weights = {
        'profit': 0.5,    # 盈利相关性权重最高
        'direction': 0.3, # 方向相关性
        'volume': 0.2     # 交易量相关性
    }
    
    comprehensive_score = (
        profit_score * weights['profit'] +
        direction_score * weights['direction'] +
        volume_score * weights['volume']
    )
    
    score_details = {
        'comprehensive_score': comprehensive_score,
        'profit_score': profit_score,
        'direction_score': direction_score,
        'volume_score': volume_score,
        'profit_explanation': profit_explanation,
        'weights': weights
    }
    
    return comprehensive_score, score_details

# 测试用例
def test_hedge_detection():
    """测试不同情况下的对敲检测"""
    
    test_cases = [
        # 测试用例1：典型对敲（一盈一亏，金额相近）
        (Position(100.0, 1000, 'long'), Position(-95.0, 1050, 'short'), "典型对敲"),
        
        # 测试用例2：双亏但金额非常相近（差距1%）
        (Position(-100.0, 1000, 'long'), Position(-101.0, 1050, 'short'), "双亏金额非常相近"),

        # 测试用例3：双亏但金额相近
        (Position(-100.0, 1000, 'long'), Position(-105.0, 1050, 'short'), "双亏金额相近"),

        # 测试用例4：双亏但差距很大
        (Position(-100.0, 1000, 'long'), Position(-10.0, 1050, 'short'), "双亏差距很大"),
        
        # 测试用例5：双盈情况
        (Position(100.0, 1000, 'long'), Position(50.0, 1050, 'short'), "双盈情况"),

        # 测试用例6：完全对冲
        (Position(100.0, 1000, 'long'), Position(-100.0, 1000, 'short'), "完全对冲"),
    ]
    
    print("=== 对敲检测测试结果 ===\n")
    
    for i, (pos_a, pos_b, description) in enumerate(test_cases, 1):
        score, details = calculate_comprehensive_hedge_score(pos_a, pos_b)
        
        print(f"测试用例 {i}: {description}")
        print(f"仓位A: 盈亏={pos_a.real_profit:.2f}, 量={pos_a.volume}, 方向={pos_a.direction}")
        print(f"仓位B: 盈亏={pos_b.real_profit:.2f}, 量={pos_b.volume}, 方向={pos_b.direction}")
        print(f"综合评分: {score:.3f}")
        print(f"盈利评分: {details['profit_score']:.3f} - {details['profit_explanation']}")
        print(f"方向评分: {details['direction_score']:.3f}")
        print(f"交易量评分: {details['volume_score']:.3f}")
        print("-" * 80)

if __name__ == "__main__":
    test_hedge_detection()
