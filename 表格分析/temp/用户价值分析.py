#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户价值分析系统
基于fp调用次数数据进行多维度用户价值分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
except:
    print("警告: 无法设置中文字体，图表可能显示乱码")

class UserValueAnalyzer:
    def __init__(self, data_path):
        """初始化用户价值分析器"""
        self.data_path = data_path
        self.df = None
        self.load_data()
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            self.df = pd.read_csv(self.data_path)
            print(f"原始数据加载成功，共{len(self.df)}条记录")

            # 排除测试账号
            test_member_id = 'e5829ad101ce4c88a65848de29352a58'
            before_count = len(self.df)
            self.df = self.df[self.df['member_id'] != test_member_id]
            after_count = len(self.df)
            excluded_count = before_count - after_count
            print(f"已排除测试账号 {test_member_id}，删除 {excluded_count} 条记录")
            print(f"清理后数据共 {after_count} 条记录")

            # 数据预处理
            self.df['created_time'] = pd.to_datetime(self.df['created_time'])
            self.df['date'] = self.df['created_time'].dt.date
            self.df['hour'] = self.df['created_time'].dt.hour

            # 处理缺失值
            self.df['city'] = self.df['city'].fillna('Unknown')
            self.df['country'] = self.df['country'].fillna('Unknown')
            self.df['device_name'] = self.df['device_name'].fillna('Unknown')

            print("数据预处理完成")

        except Exception as e:
            print(f"数据加载失败: {e}")
            
    def analyze_by_user_time_platform(self):
        """按用户、时间、登录平台分析"""
        print("\n=== 用户、时间、平台分析 ===")
        
        # 1. 用户活跃度分析
        user_activity = self.df.groupby('member_id').agg({
            'id': 'count',  # 总调用次数
            'action_type': lambda x: x.nunique(),  # 不同操作类型数
            'created_time': ['min', 'max'],  # 首次和最后活跃时间
            'date': lambda x: x.nunique()  # 活跃天数
        }).round(2)
        
        user_activity.columns = ['总调用次数', '操作类型数', '首次活跃时间', '最后活跃时间', '活跃天数']
        
        # 计算用户价值评分
        user_activity['日均调用次数'] = user_activity['总调用次数'] / user_activity['活跃天数']
        user_activity['用户价值评分'] = (
            user_activity['总调用次数'] * 0.4 +
            user_activity['活跃天数'] * 0.3 +
            user_activity['操作类型数'] * 0.2 +
            user_activity['日均调用次数'] * 0.1
        )
        
        # 用户分层
        user_activity['用户等级'] = pd.cut(
            user_activity['用户价值评分'],
            bins=[0, 10, 50, 200, float('inf')],
            labels=['低价值', '中价值', '高价值', '超高价值']
        )
        
        print("用户价值分层统计:")
        print(user_activity['用户等级'].value_counts())
        
        # 2. 时间维度分析
        daily_stats = self.df.groupby('date').agg({
            'id': 'count',
            'member_id': 'nunique'
        })
        daily_stats.columns = ['日调用次数', '日活跃用户数']
        daily_stats['人均调用次数'] = daily_stats['日调用次数'] / daily_stats['日活跃用户数']
        
        # 3. 平台分析
        platform_stats = self.df.groupby('via').agg({
            'id': 'count',
            'member_id': 'nunique'
        })
        platform_stats.columns = ['平台调用次数', '平台用户数']
        platform_stats['平台人均调用'] = platform_stats['平台调用次数'] / platform_stats['平台用户数']
        platform_stats = platform_stats.sort_values('平台调用次数', ascending=False)
        
        print("\n平台使用情况:")
        print(platform_stats)
        
        # 4. 操作类型分析
        action_stats = self.df.groupby('action_type').agg({
            'id': 'count',
            'member_id': 'nunique'
        })
        action_stats.columns = ['操作次数', '操作用户数']
        action_stats = action_stats.sort_values('操作次数', ascending=False)
        
        print("\n操作类型统计:")
        print(action_stats)
        
        return user_activity, daily_stats, platform_stats, action_stats
    
    def analyze_by_region_device(self):
        """按区域和设备分析用户价值"""
        print("\n=== 区域和设备用户价值分析 ===")
        
        # 1. 区域分析
        region_stats = self.df.groupby(['country', 'city']).agg({
            'id': 'count',
            'member_id': 'nunique'
        }).reset_index()
        region_stats.columns = ['国家', '城市', '调用次数', '用户数']
        region_stats['人均调用次数'] = region_stats['调用次数'] / region_stats['用户数']
        region_stats = region_stats.sort_values('调用次数', ascending=False)
        
        # 国家级别统计
        country_stats = self.df.groupby('country').agg({
            'id': 'count',
            'member_id': 'nunique',
            'city': 'nunique'
        })
        country_stats.columns = ['国家调用次数', '国家用户数', '覆盖城市数']
        country_stats['国家人均调用'] = country_stats['国家调用次数'] / country_stats['国家用户数']
        country_stats = country_stats.sort_values('国家调用次数', ascending=False)
        
        print("Top 10 国家统计:")
        print(country_stats.head(10))
        
        # 2. 设备分析
        device_stats = self.df.groupby(['os', 'device_name']).agg({
            'id': 'count',
            'member_id': 'nunique'
        }).reset_index()
        device_stats.columns = ['操作系统', '设备名称', '调用次数', '用户数']
        device_stats['设备人均调用'] = device_stats['调用次数'] / device_stats['用户数']
        device_stats = device_stats.sort_values('调用次数', ascending=False)
        
        # 操作系统统计
        os_stats = self.df.groupby('os').agg({
            'id': 'count',
            'member_id': 'nunique'
        })
        os_stats.columns = ['系统调用次数', '系统用户数']
        os_stats['系统人均调用'] = os_stats['系统调用次数'] / os_stats['系统用户数']
        os_stats = os_stats.sort_values('系统调用次数', ascending=False)
        
        print("\n操作系统统计:")
        print(os_stats)
        
        # 3. 浏览器分析
        browser_stats = self.df.groupby('browser_name').agg({
            'id': 'count',
            'member_id': 'nunique'
        })
        browser_stats.columns = ['浏览器调用次数', '浏览器用户数']
        browser_stats['浏览器人均调用'] = browser_stats['浏览器调用次数'] / browser_stats['浏览器用户数']
        browser_stats = browser_stats.sort_values('浏览器调用次数', ascending=False)
        
        print("\n浏览器使用统计:")
        print(browser_stats.head(10))
        
        return region_stats, country_stats, device_stats, os_stats, browser_stats
    
    def generate_visualizations(self, user_activity, daily_stats, platform_stats, country_stats, os_stats):
        """生成可视化图表"""
        print("\n正在生成可视化图表...")
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('用户价值分析报告', fontsize=16, fontweight='bold')
        
        # 1. 用户等级分布
        user_level_counts = user_activity['用户等级'].value_counts()
        axes[0, 0].pie(user_level_counts.values, labels=user_level_counts.index, autopct='%1.1f%%')
        axes[0, 0].set_title('用户价值等级分布')
        
        # 2. 日活跃趋势
        daily_stats_plot = daily_stats.tail(30)  # 最近30天
        axes[0, 1].plot(daily_stats_plot.index, daily_stats_plot['日活跃用户数'], marker='o')
        axes[0, 1].set_title('最近30天日活跃用户趋势')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. 平台使用分布
        platform_top = platform_stats.head(8)
        axes[0, 2].bar(range(len(platform_top)), platform_top['平台调用次数'])
        axes[0, 2].set_title('各平台调用次数')
        axes[0, 2].set_xticks(range(len(platform_top)))
        axes[0, 2].set_xticklabels(platform_top.index, rotation=45)
        
        # 4. 国家分布
        country_top = country_stats.head(10)
        axes[1, 0].barh(range(len(country_top)), country_top['国家调用次数'])
        axes[1, 0].set_title('Top 10 国家调用次数')
        axes[1, 0].set_yticks(range(len(country_top)))
        axes[1, 0].set_yticklabels(country_top.index)
        
        # 5. 操作系统分布
        axes[1, 1].pie(os_stats['系统调用次数'].values, labels=os_stats.index, autopct='%1.1f%%')
        axes[1, 1].set_title('操作系统使用分布')
        
        # 6. 用户价值评分分布
        axes[1, 2].hist(user_activity['用户价值评分'], bins=50, alpha=0.7)
        axes[1, 2].set_title('用户价值评分分布')
        axes[1, 2].set_xlabel('价值评分')
        axes[1, 2].set_ylabel('用户数量')
        
        plt.tight_layout()
        plt.savefig('用户价值分析图表.png', dpi=300, bbox_inches='tight')
        print("图表已保存到: 用户价值分析图表.png")
        
    def generate_report(self):
        """生成完整分析报告"""
        print("开始生成用户价值分析报告...")
        
        # 执行分析
        user_activity, daily_stats, platform_stats, action_stats = self.analyze_by_user_time_platform()
        region_stats, country_stats, device_stats, os_stats, browser_stats = self.analyze_by_region_device()
        
        # 生成可视化
        self.generate_visualizations(user_activity, daily_stats, platform_stats, country_stats, os_stats)
        
        # 保存详细数据
        with pd.ExcelWriter('用户价值分析详细数据.xlsx') as writer:
            user_activity.to_excel(writer, sheet_name='用户活跃度分析')
            daily_stats.to_excel(writer, sheet_name='日活跃趋势')
            platform_stats.to_excel(writer, sheet_name='平台分析')
            action_stats.to_excel(writer, sheet_name='操作类型分析')
            country_stats.to_excel(writer, sheet_name='国家分析')
            os_stats.to_excel(writer, sheet_name='操作系统分析')
            browser_stats.to_excel(writer, sheet_name='浏览器分析')
            region_stats.head(100).to_excel(writer, sheet_name='区域分析Top100')
            device_stats.head(100).to_excel(writer, sheet_name='设备分析Top100')
        
        print("详细数据已保存到: 用户价值分析详细数据.xlsx")
        
        # 生成总结报告
        self.generate_summary_report(user_activity, country_stats, os_stats, platform_stats)
        
    def generate_summary_report(self, user_activity, country_stats, os_stats, platform_stats):
        """生成总结报告"""
        total_users = len(user_activity)
        total_calls = user_activity['总调用次数'].sum()
        avg_calls_per_user = total_calls / total_users
        
        high_value_users = len(user_activity[user_activity['用户等级'].isin(['高价值', '超高价值'])])
        high_value_ratio = high_value_users / total_users * 100
        
        top_country = country_stats.index[0]
        top_os = os_stats.index[0]
        top_platform = platform_stats.index[0]
        
        report = f"""
# 用户价值分析总结报告

## 核心指标
- 总用户数: {total_users:,}
- 总调用次数: {total_calls:,}
- 人均调用次数: {avg_calls_per_user:.2f}
- 高价值用户数: {high_value_users:,} ({high_value_ratio:.1f}%)

## 用户分层结果
{user_activity['用户等级'].value_counts().to_string()}

## 地域分布
- 主要国家: {top_country}
- 覆盖国家数: {len(country_stats)}

## 技术特征
- 主要操作系统: {top_os}
- 主要平台: {top_platform}

## 建议
1. 重点关注高价值用户的需求和体验
2. 针对主要地区({top_country})制定本地化策略
3. 优化{top_os}和{top_platform}平台的用户体验
4. 提升中低价值用户的活跃度和粘性

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        with open('用户价值分析总结报告.md', 'w', encoding='utf-8') as f:
            f.write(report)

        print("总结报告已保存到: 用户价值分析总结报告.md")
        print(report)

if __name__ == "__main__":
    # 创建分析器并运行分析
    analyzer = UserValueAnalyzer('data/pf调用次数.csv')
    analyzer.generate_report()
