#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
融合改进的对敲检测算法
基于原始公式，补充完整的逻辑处理
"""

from dataclasses import dataclass
from typing import Tuple, Optional

@dataclass
class Position:
    """仓位信息"""
    real_profit: float  # 实际盈亏
    volume: float = 0   # 交易量
    direction: str = '' # 方向：'long' 或 'short'

def calculate_profit_hedge_score(pos_a: Position, pos_b: Position) -> Tuple[float, dict]:
    """
    改进的对敲盈利评分算法
    基于原始公式，融合完整的逻辑处理
    
    Args:
        pos_a: 仓位A
        pos_b: 仓位B
    
    Returns:
        Tuple[float, dict]: (最终评分, 详细信息)
    """
    
    # === 原始公式部分 ===
    total_profit = pos_a.real_profit + pos_b.real_profit
    profit_sum = abs(pos_a.real_profit) + abs(pos_b.real_profit)
    
    # 处理边界情况
    if profit_sum == 0:
        return 0.0, {
            'base_score': 0.0,
            'adjusted_score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'case_type': '无盈亏',
            'risk_level': '无法判断'
        }
    
    # 基础对敲评分：总盈亏越接近0，评分越高
    profit_hedge_score = 1.0 - abs(total_profit) / profit_sum
    
    # === 改进的逻辑处理部分 ===
    pos_a_profit = pos_a.real_profit
    pos_b_profit = pos_b.real_profit
    
    # 根据盈亏组合情况进行调整
    if pos_a_profit > 0 and pos_b_profit > 0:
        # 双盈情况：两个盈利都越接近0，对敲可能性越高
        case_type = "双盈"
        max_profit = max(abs(pos_a_profit), abs(pos_b_profit))
        
        if max_profit <= 10:  # 都接近0，可能是对敲
            adjustment_factor = 0.9
            explanation = f"双盈且都接近0，有对敲可能 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
        elif max_profit <= 50:  # 金额较小
            adjustment_factor = 0.6
            explanation = f"双盈且金额较小，中等对敲可能 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
        else:  # 金额较大，不太可能是对敲
            adjustment_factor = 0.3
            explanation = f"双盈且金额较大，对敲可能性较低 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
            
    elif pos_a_profit < 0 and pos_b_profit < 0:
        # 双亏情况：两个亏损都越接近0，对敲可能性越高
        case_type = "双亏"
        max_loss = max(abs(pos_a_profit), abs(pos_b_profit))
        
        if max_loss <= 10:  # 都接近0，可能是对敲
            adjustment_factor = 0.9
            explanation = f"双亏且都接近0，有对敲可能 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
        elif max_loss <= 50:  # 金额较小
            adjustment_factor = 0.6
            explanation = f"双亏且金额较小，中等对敲可能 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
        else:  # 金额较大，不太可能是对敲
            adjustment_factor = 0.3
            explanation = f"双亏且金额较大，对敲可能性较低 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
            
    else:
        # 一盈一亏情况：最符合对敲特征
        case_type = "一盈一亏"
        
        if abs(total_profit) < min(abs(pos_a_profit), abs(pos_b_profit)) * 0.1:
            # 盈亏几乎完全抵消
            adjustment_factor = 1.2  # 提高评分
            explanation = f"一盈一亏且几乎完全抵消，对敲特征明显 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
        else:
            adjustment_factor = 1.0  # 保持原评分
            explanation = f"一盈一亏，符合对敲特征 (A:{pos_a_profit:.2f}, B:{pos_b_profit:.2f})"
    
    # 应用调整因子
    adjusted_score = min(profit_hedge_score * adjustment_factor, 1.0)
    
    # === 可选的额外因子 ===
    direction_bonus = 0.0
    volume_bonus = 0.0
    
    # 方向因子：相反方向更符合对敲特征
    if pos_a.direction and pos_b.direction:
        if pos_a.direction != pos_b.direction:  # 相反方向
            direction_bonus = 0.05
        else:  # 相同方向
            direction_bonus = -0.02
    
    # 交易量因子：相近交易量更符合对敲特征
    if pos_a.volume > 0 and pos_b.volume > 0:
        volume_sum = pos_a.volume + pos_b.volume
        volume_diff = abs(pos_a.volume - pos_b.volume)
        volume_similarity = 1.0 - (volume_diff / volume_sum)
        if volume_similarity > 0.8:  # 交易量相近
            volume_bonus = 0.03
    
    # 最终评分
    final_score = min(max(adjusted_score + direction_bonus + volume_bonus, 0.0), 1.0)
    
    # 风险等级判定
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    # 返回详细信息
    details = {
        'base_score': profit_hedge_score,
        'adjusted_score': adjusted_score,
        'final_score': final_score,
        'adjustment_factor': adjustment_factor,
        'direction_bonus': direction_bonus,
        'volume_bonus': volume_bonus,
        'explanation': explanation,
        'case_type': case_type,
        'risk_level': risk_level,
        'total_profit': total_profit,
        'profit_sum': profit_sum
    }
    
    return final_score, details


def test_integrated_algorithm():
    """测试融合后的算法"""
    
    print("=== 融合改进的对敲检测算法测试 ===\n")
    
    test_cases = [
        # 一盈一亏情况
        (Position(100.0, 1000, 'long'), Position(-95.0, 1050, 'short'), "典型对敲（一盈一亏）"),
        (Position(100.0, 1000, 'long'), Position(-100.0, 1000, 'short'), "完全对冲（一盈一亏）"),
        
        # 双盈情况
        (Position(2.0, 1000, 'long'), Position(3.0, 1050, 'short'), "双盈且都接近0"),
        (Position(20.0, 1000, 'long'), Position(25.0, 1050, 'short'), "双盈且金额较小"),
        (Position(100.0, 1000, 'long'), Position(120.0, 1050, 'short'), "双盈且金额较大"),
        
        # 双亏情况
        (Position(-2.0, 1000, 'long'), Position(-3.0, 1050, 'short'), "双亏且都接近0"),
        (Position(-20.0, 1000, 'long'), Position(-25.0, 1050, 'short'), "双亏且金额较小"),
        (Position(-100.0, 1000, 'long'), Position(-120.0, 1050, 'short'), "双亏且金额较大"),
    ]
    
    for i, (pos_a, pos_b, description) in enumerate(test_cases, 1):
        score, details = calculate_profit_hedge_score(pos_a, pos_b)
        
        print(f"测试用例 {i}: {description}")
        print(f"仓位A: 盈亏={pos_a.real_profit:.2f}, 量={pos_a.volume}, 方向={pos_a.direction}")
        print(f"仓位B: 盈亏={pos_b.real_profit:.2f}, 量={pos_b.volume}, 方向={pos_b.direction}")
        print(f"基础评分: {details['base_score']:.3f} (原始公式)")
        print(f"调整评分: {details['adjusted_score']:.3f} (调整因子: {details['adjustment_factor']:.1f})")
        print(f"最终评分: {details['final_score']:.3f}")
        print(f"风险等级: {details['risk_level']}")
        print(f"情况类型: {details['case_type']}")
        print(f"说明: {details['explanation']}")
        print("-" * 80)


if __name__ == "__main__":
    test_integrated_algorithm()
