#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终的对敲检测算法
基于原始公式，融合完整的改进逻辑
"""

def calculate_hedge_score_final(pos_a_profit, pos_b_profit):
    """
    最终的对敲检测算法
    基于你的原始公式，补充完整的逻辑处理
    
    Args:
        pos_a_profit: 仓位A的实际盈亏
        pos_b_profit: 仓位B的实际盈亏
    
    Returns:
        dict: 包含评分和详细信息
    """
    
    # === 你的原始公式 ===
    total_profit = pos_a_profit + pos_b_profit
    profit_sum = abs(pos_a_profit) + abs(pos_b_profit)
    
    if profit_sum == 0:
        return {
            'score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'case_type': '无盈亏'
        }
    
    # 基础对敲评分：总盈亏越接近0，评分越高
    profit_hedge_score = 1.0 - abs(total_profit) / profit_sum
    
    # === 补充的完整逻辑 ===
    
    if pos_a_profit > 0 and pos_b_profit > 0:
        # 双盈情况：两个盈利都越接近0，对敲可能性越高
        case_type = "双盈"
        max_profit = max(abs(pos_a_profit), abs(pos_b_profit))
        
        if max_profit <= 10:  # 都接近0
            adjustment_factor = 0.9
            explanation = f"双盈且都接近0，有对敲可能"
        elif max_profit <= 50:  # 金额较小
            adjustment_factor = 0.6
            explanation = f"双盈且金额较小，中等对敲可能"
        else:  # 金额较大
            adjustment_factor = 0.3
            explanation = f"双盈且金额较大，对敲可能性较低"
            
    elif pos_a_profit < 0 and pos_b_profit < 0:
        # 双亏情况：两个亏损都越接近0，对敲可能性越高
        case_type = "双亏"
        max_loss = max(abs(pos_a_profit), abs(pos_b_profit))
        
        if max_loss <= 10:  # 都接近0
            adjustment_factor = 0.9
            explanation = f"双亏且都接近0，有对敲可能"
        elif max_loss <= 50:  # 金额较小
            adjustment_factor = 0.6
            explanation = f"双亏且金额较小，中等对敲可能"
        else:  # 金额较大
            adjustment_factor = 0.3
            explanation = f"双亏且金额较大，对敲可能性较低"
            
    else:
        # 一盈一亏情况：最符合对敲特征
        case_type = "一盈一亏"
        
        if abs(total_profit) < min(abs(pos_a_profit), abs(pos_b_profit)) * 0.1:
            # 盈亏几乎完全抵消
            adjustment_factor = 1.2
            explanation = f"一盈一亏且几乎完全抵消，对敲特征明显"
        else:
            adjustment_factor = 1.0
            explanation = f"一盈一亏，符合对敲特征"
    
    # 最终评分
    final_score = min(profit_hedge_score * adjustment_factor, 1.0)
    
    # 风险等级
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    return {
        'score': final_score,
        'base_score': profit_hedge_score,
        'adjustment_factor': adjustment_factor,
        'explanation': explanation,
        'case_type': case_type,
        'risk_level': risk_level,
        'total_profit': total_profit,
        'profit_sum': profit_sum
    }


# 使用示例
def demo_final_algorithm():
    """演示最终算法"""
    
    print("=== 最终对敲检测算法演示 ===\n")
    print("基于你的原始公式：profit_hedge_score = 1.0 - abs(total_profit) / profit_sum")
    print("补充完整的逻辑处理\n")
    
    test_cases = [
        (100.0, -95.0, "典型对敲"),
        (100.0, -100.0, "完全对冲"),
        (2.0, 3.0, "双盈接近0"),
        (100.0, 120.0, "双盈较大"),
        (-2.0, -3.0, "双亏接近0"),
        (-100.0, -120.0, "双亏较大"),
    ]
    
    for pos_a, pos_b, desc in test_cases:
        result = calculate_hedge_score_final(pos_a, pos_b)
        
        print(f"{desc}: A={pos_a:.1f}, B={pos_b:.1f}")
        print(f"  基础评分: {result['base_score']:.3f} (原始公式)")
        print(f"  调整因子: {result['adjustment_factor']:.1f}")
        print(f"  最终评分: {result['score']:.3f}")
        print(f"  风险等级: {result['risk_level']}")
        print(f"  情况类型: {result['case_type']}")
        print(f"  说明: {result['explanation']}")
        print()


if __name__ == "__main__":
    demo_final_algorithm()
