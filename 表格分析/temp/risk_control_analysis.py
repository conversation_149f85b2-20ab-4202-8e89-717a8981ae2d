# -*- coding: utf-8 -*-
"""
风控策略效果分析脚本
专门用于监控风控策略的效果和趋势
"""

import pandas as pd
import numpy as np
from datetime import datetime

def analyze_risk_control_trends():
    """分析风控趋势"""
    
    # 读取分析结果
    df = pd.read_csv('output/analysis_result_20250709_153310.csv')
    
    print("=" * 60)
    print("风控策略效果分析报告")
    print("=" * 60)
    
    # 1. 整体风控效果概览
    print("\n1. 整体风控效果概览")
    print("-" * 40)
    
    total_register = df['注册用户数'].sum()
    total_problems = df['问题用户数'].sum()
    overall_alert_rate = (total_problems / total_register * 100) if total_register > 0 else 0
    
    print(f"总注册用户数: {total_register:,}")
    print(f"总问题用户数: {total_problems:,}")
    print(f"整体风控警报密度: {overall_alert_rate:.2f}%")
    
    # 2. 按日期汇总的趋势分析
    print("\n2. 每日风控趋势分析")
    print("-" * 40)
    
    daily_summary = df.groupby('日期').agg({
        '注册用户数': 'sum',
        '问题用户数': 'sum',
        'KYC用户数': 'sum',
        '参与活动用户数': 'sum',
        '活动奖励总额': 'sum',
        '问题用户奖励总额': 'sum',
        '同IP用户数': 'sum',
        '同设备ID用户数': 'sum',
        '邮箱相似用户数': 'sum'
    }).reset_index()
    
    # 计算每日风控警报密度
    daily_summary['风控警报密度(%)'] = (daily_summary['问题用户数'] / daily_summary['注册用户数'] * 100).round(2)
    daily_summary['KYC完成率(%)'] = (daily_summary['KYC用户数'] / daily_summary['注册用户数'] * 100).round(2)
    daily_summary['新用户活跃率(%)'] = (daily_summary['参与活动用户数'] / daily_summary['注册用户数'] * 100).round(2)
    
    # 显示最近10天的趋势
    recent_days = daily_summary.tail(10)
    print("最近10天风控指标:")
    print(recent_days[['日期', '注册用户数', '问题用户数', '风控警报密度(%)', 'KYC完成率(%)', '新用户活跃率(%)']].to_string(index=False))
    
    # 3. 风控警报密度异常检测
    print("\n3. 风控警报密度异常检测")
    print("-" * 40)
    
    alert_rates = daily_summary['风控警报密度(%)']
    mean_rate = alert_rates.mean()
    std_rate = alert_rates.std()
    
    print(f"平均风控警报密度: {mean_rate:.2f}%")
    print(f"标准差: {std_rate:.2f}%")
    
    # 找出异常高的日期（超过平均值+2倍标准差）
    high_alert_threshold = mean_rate + 2 * std_rate
    high_alert_days = daily_summary[daily_summary['风控警报密度(%)'] > high_alert_threshold]
    
    if len(high_alert_days) > 0:
        print(f"\n⚠️  风控警报密度异常高的日期 (>{high_alert_threshold:.2f}%):")
        print(high_alert_days[['日期', '注册用户数', '问题用户数', '风控警报密度(%)']].to_string(index=False))
    else:
        print("✅ 没有发现风控警报密度异常高的日期")
    
    # 4. 按国家分析风控效果
    print("\n4. 按国家分析风控效果")
    print("-" * 40)
    
    country_summary = df.groupby('国家').agg({
        '注册用户数': 'sum',
        '问题用户数': 'sum',
        'KYC用户数': 'sum',
        '活动奖励总额': 'sum',
        '问题用户奖励总额': 'sum'
    }).reset_index()
    
    # 只分析注册用户数>100的国家
    country_summary = country_summary[country_summary['注册用户数'] > 100]
    country_summary['风控警报密度(%)'] = (country_summary['问题用户数'] / country_summary['注册用户数'] * 100).round(2)
    country_summary['问题用户奖励占比(%)'] = (country_summary['问题用户奖励总额'] / country_summary['活动奖励总额'] * 100).round(2)
    
    # 按风控警报密度排序
    country_summary = country_summary.sort_values('风控警报密度(%)', ascending=False)
    
    print("主要国家风控效果 (注册用户>100):")
    print(country_summary[['国家', '注册用户数', '问题用户数', '风控警报密度(%)', '问题用户奖励占比(%)']].head(10).to_string(index=False))
    
    # 5. 问题类型分析
    print("\n5. 问题类型分析")
    print("-" * 40)
    
    problem_type_summary = {
        '同IP问题': df['同IP用户数'].sum(),
        '同设备ID问题': df['同设备ID用户数'].sum(),
        '邮箱相似问题': df['邮箱相似用户数'].sum(),
    }
    
    total_problem_instances = sum(problem_type_summary.values())
    
    print("问题类型分布:")
    for problem_type, count in problem_type_summary.items():
        percentage = (count / total_problem_instances * 100) if total_problem_instances > 0 else 0
        print(f"  {problem_type}: {count:,} ({percentage:.1f}%)")
    
    # 6. 风控策略建议
    print("\n6. 风控策略建议")
    print("-" * 40)
    
    # 基于数据给出建议
    if overall_alert_rate > 10:
        print("🔴 整体风控警报密度较高，建议:")
        print("   - 检查风控规则是否过于严格")
        print("   - 分析误报情况")
        print("   - 优化风控算法")
    elif overall_alert_rate < 1:
        print("🟡 整体风控警报密度较低，建议:")
        print("   - 检查风控规则是否过于宽松")
        print("   - 加强异常行为检测")
        print("   - 提高风控敏感度")
    else:
        print("🟢 整体风控警报密度适中")
    
    # 检查问题用户奖励占比
    total_bonus = df['活动奖励总额'].sum()
    problem_bonus = df['问题用户奖励总额'].sum()
    problem_bonus_ratio = (problem_bonus / total_bonus * 100) if total_bonus > 0 else 0
    
    if problem_bonus_ratio > 30:
        print(f"🔴 问题用户获得了{problem_bonus_ratio:.1f}%的奖励，建议:")
        print("   - 加强活动参与前的风控检查")
        print("   - 实施奖励发放前的二次验证")
    elif problem_bonus_ratio > 15:
        print(f"🟡 问题用户获得了{problem_bonus_ratio:.1f}%的奖励，需要关注")
    else:
        print(f"🟢 问题用户奖励占比{problem_bonus_ratio:.1f}%，控制良好")
    
    return daily_summary, country_summary

def generate_risk_control_dashboard():
    """生成风控监控仪表板数据"""
    
    df = pd.read_csv('output/analysis_result_20250709_153310.csv')
    
    # 按日期汇总
    daily_summary = df.groupby('日期').agg({
        '注册用户数': 'sum',
        '问题用户数': 'sum',
        'KYC用户数': 'sum',
        '参与活动用户数': 'sum',
        '同IP用户数': 'sum',
        '同设备ID用户数': 'sum',
        '邮箱相似用户数': 'sum'
    }).reset_index()
    
    daily_summary['风控警报密度(%)'] = (daily_summary['问题用户数'] / daily_summary['注册用户数'] * 100).round(2)
    
    # 保存仪表板数据
    dashboard_file = 'output/risk_control_dashboard.csv'
    daily_summary.to_csv(dashboard_file, index=False, encoding='utf-8-sig')
    print(f"\n风控监控仪表板数据已保存到: {dashboard_file}")
    
    return daily_summary

if __name__ == "__main__":
    try:
        # 执行风控分析
        daily_summary, country_summary = analyze_risk_control_trends()
        
        # 生成仪表板数据
        dashboard_data = generate_risk_control_dashboard()
        
        print(f"\n{'='*60}")
        print("风控分析完成！")
        print("建议定期运行此分析以监控风控策略效果")
        print(f"{'='*60}")
        
    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
