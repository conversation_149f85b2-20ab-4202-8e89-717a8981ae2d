# 功能模块分类说明

本文档说明了temp目录下各个功能模块的分类和用途，方便后期查找和调用。

## 📁 文件夹结构概览

```
temp/
├── 用户数据分析系统/          # 核心用户数据分析功能
├── 对敲检测算法/             # 各种对敲检测算法实现
├── 高频交易分析/             # 高频交易用户分析
├── 风险控制分析/             # 风控策略效果分析
├── 数据处理工具/             # 数据读取和处理工具
├── 报告文档/                # 项目文档和分析报告
├── 配置和依赖/              # 项目配置文件
├── 测试文件/                # 测试相关文件
├── data/                   # 原始数据文件
├── output/                 # 输出结果文件
└── charts/                 # 图表文件
```

## 🔧 各模块功能详解

### 1. 用户数据分析系统 📊
**主要功能**: 平台用户注册、活动参与、问题用户等多维度数据分析

**核心文件**:
- `main.py` - 主程序入口，支持命令行参数
- `data_processor.py` - 数据处理模块，负责数据清洗和预处理
- `indicator_calculator.py` - 指标计算模块，计算各种业务指标
- `report_generator.py` - 报告生成模块，输出多格式报告
- `config.py` - 配置文件，定义系统参数

**专项分析工具**:
- `most_freq_via_analyzer.py` - 问题用户访问渠道分析
- `check_most_freq_via.py` - 访问渠道数据检查
- `debug_problem_users.py` - 问题用户调试工具
- `super_user_detailed_analysis.py` - 超级用户详细分析

**使用场景**: 日常用户数据分析、业务指标监控、问题用户识别

### 2. 对敲检测算法 🎯
**主要功能**: 检测用户交易中的对敲行为，防范风险

**算法分类**:

**核心算法**:
- `核心对敲检测算法_最终版.py` - 最终优化版本 ⭐
- `核心对敲检测算法.py` - 基础版本

**改进算法**:
- `improved_hedge_detection.py` - 改进的对敲检测
- `优化对敲检测算法.py` - 优化版算法
- `改进的对敲检测公式.py` - 改进公式版本

**专项算法**:
- `三要素对敲检测算法.py` - 基于三要素的检测
- `双因子对敲检测算法.py` - 双因子检测方法
- `相对波动率加接近0的对敲检测.py` - 基于波动率的检测
- `同边异边分流对敲检测算法.py` - 分流检测算法

**测试文件**:
- `对敲算法测试.py` - 基础测试
- `参数调整对比测试.py` - 参数对比测试
- `综合优化对敲检测测试.py` - 综合测试

**使用场景**: 风控系统、交易监控、异常行为检测

### 3. 高频交易分析 📈
**主要功能**: 分析高频交易用户行为，识别异常交易模式

**核心文件**:
- `high_frequency_trading_analysis.py` - 高频交易分析主程序

**分析报告**:
- `高频交易分析报告.txt` - 分析结果报告
- `超高频用户分析报告.txt` - 超高频用户专项报告
- `超高频用户综合分析报告.txt` - 综合分析报告

**数据文件**:
- `高频交易详细数据.xlsx` - 详细交易数据
- `超高频用户详细数据.xlsx` - 超高频用户数据

**使用场景**: 交易行为监控、异常用户识别、风险预警

### 4. 风险控制分析 🛡️
**主要功能**: 监控风控策略效果，分析风控趋势

**核心文件**:
- `risk_control_analysis.py` - 风控策略效果分析

**使用场景**: 风控策略评估、风险趋势监控、策略优化

### 5. 数据处理工具 🔧
**主要功能**: 数据读取、格式转换、数据检查

**核心文件**:
- `read_excel_data.py` - Excel数据读取工具
- `check_excel_sheets.py` - Excel工作表检查工具

**使用场景**: 数据预处理、格式转换、数据质量检查

### 6. 报告文档 📋
**主要功能**: 项目文档、分析报告、使用说明

**文档类型**:
- `README.md` - 项目总体说明
- `用户数据分析系统开发文档.md` - 开发文档
- `对敲检测算法改进分析.md` - 算法改进分析
- `最终分析报告.md` - 项目分析报告

**使用场景**: 项目文档查阅、功能说明、分析结果展示

### 7. 配置和依赖 ⚙️
**主要功能**: 项目配置和依赖管理

**核心文件**:
- `requirements.txt` - Python依赖包列表

**使用场景**: 环境搭建、依赖安装

## 🚀 快速使用指南

### 用户数据分析
```bash
cd 用户数据分析系统
python main.py --days 30
```

### 对敲检测
```bash
cd 对敲检测算法
python 核心对敲检测算法_最终版.py
```

### 高频交易分析
```bash
cd 高频交易分析
python high_frequency_trading_analysis.py
```

### 风险控制分析
```bash
cd 风险控制分析
python risk_control_analysis.py
```

## 📝 注意事项

1. **数据依赖**: 大部分分析工具依赖data目录下的数据文件
2. **环境要求**: 需要安装requirements.txt中的依赖包
3. **输出目录**: 分析结果会输出到output目录
4. **图表生成**: 图表文件会保存到charts目录

## 🔄 更新记录

- 2025-07-31: 完成功能模块分类整理
- 按功能将相关文件归类到对应文件夹
- 创建功能说明文档，便于后期查找调用
