#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from config import *

def debug_problem_users():
    """
    调试问题用户识别逻辑
    """
    try:
        # 读取问题用户表
        df_problem = pd.read_excel('真实数据表格.xlsx', sheet_name='问题用户表格')
        print(f"问题用户表总数据量: {len(df_problem)}")
        
        # 检查各个条件的分布
        print("\n=== 各字段数据分布 ===")
        
        # 条件1: IP数量
        print(f"\nIP数量分布:")
        print(f"ip_cnt字段统计:")
        print(df_problem['ip_cnt'].describe())
        condition1 = df_problem['ip_cnt'] > PROBLEM_USER_CONDITIONS['ip_cnt_threshold']
        print(f"IP数量 > {PROBLEM_USER_CONDITIONS['ip_cnt_threshold']} 的用户数: {condition1.sum()}")
        
        # 条件2: 设备ID数量
        print(f"\n设备ID数量分布:")
        print(f"device_id_cnt字段统计:")
        print(df_problem['device_id_cnt'].describe())
        condition2 = df_problem['device_id_cnt'] > PROBLEM_USER_CONDITIONS['device_id_cnt_threshold']
        print(f"设备ID数量 > {PROBLEM_USER_CONDITIONS['device_id_cnt_threshold']} 的用户数: {condition2.sum()}")
        
        # 条件3: 邮箱相似
        print(f"\n邮箱相似分布:")
        print(df_problem['similar_email'].value_counts(dropna=False))
        condition3 = df_problem['similar_email'].str.upper() == PROBLEM_USER_CONDITIONS['similar_email_flag'].upper()
        print(f"邮箱相似 = '{PROBLEM_USER_CONDITIONS['similar_email_flag']}' 的用户数: {condition3.sum()}")
        
        # 条件4: 黑灰名单
        print(f"\n黑灰名单分布:")
        print(df_problem['black_grey_flag'].value_counts(dropna=False))
        condition4 = (df_problem['black_grey_flag'].notna()) & (df_problem['black_grey_flag'] != '') & (df_problem['black_grey_flag'] != 'nan')
        print(f"黑灰名单非空的用户数: {condition4.sum()}")
        
        # 条件5: 风险标签
        print(f"\n风险标签分布:")
        print(df_problem['risk_label'].value_counts(dropna=False))
        condition5 = (df_problem['risk_label'].notna()) & (df_problem['risk_label'] != '') & (df_problem['risk_label'] != 'nan')
        print(f"风险标签非空的用户数: {condition5.sum()}")
        
        # 条件6: 用户风险标识
        print(f"\n用户风险标识分布:")
        print(df_problem['user_risk_flag'].value_counts(dropna=False))
        condition6 = (df_problem['user_risk_flag'].notna()) & (df_problem['user_risk_flag'] != '') & (df_problem['user_risk_flag'] != 'nan')
        print(f"用户风险标识非空的用户数: {condition6.sum()}")
        
        # 总的问题用户
        problem_mask = condition1 | condition2 | condition3 | condition4 | condition5 | condition6
        print(f"\n=== 总结 ===")
        print(f"满足任一条件的问题用户数: {problem_mask.sum()}")
        print(f"问题用户比例: {problem_mask.sum() / len(df_problem) * 100:.2f}%")
        
        # 各条件的重叠情况
        print(f"\n=== 条件重叠分析 ===")
        print(f"只满足IP条件: {(condition1 & ~condition2 & ~condition3 & ~condition4 & ~condition5 & ~condition6).sum()}")
        print(f"只满足设备ID条件: {(~condition1 & condition2 & ~condition3 & ~condition4 & ~condition5 & ~condition6).sum()}")
        print(f"只满足邮箱相似条件: {(~condition1 & ~condition2 & condition3 & ~condition4 & ~condition5 & ~condition6).sum()}")
        print(f"只满足黑灰名单条件: {(~condition1 & ~condition2 & ~condition3 & condition4 & ~condition5 & ~condition6).sum()}")
        print(f"只满足风险标签条件: {(~condition1 & ~condition2 & ~condition3 & ~condition4 & condition5 & ~condition6).sum()}")
        print(f"只满足用户风险标识条件: {(~condition1 & ~condition2 & ~condition3 & ~condition4 & ~condition5 & condition6).sum()}")
        
        # 如果所有用户都是问题用户，检查数据质量
        if problem_mask.sum() == len(df_problem):
            print(f"\n⚠️  警告：所有用户都被识别为问题用户！")
            print("可能的原因：")
            print("1. 阈值设置过低")
            print("2. 数据质量问题")
            print("3. 业务逻辑需要调整")
            
            # 建议调整阈值
            print(f"\n建议的阈值调整：")
            print(f"当前IP阈值: {PROBLEM_USER_CONDITIONS['ip_cnt_threshold']}, 建议调整为: {int(df_problem['ip_cnt'].quantile(0.9))}")
            print(f"当前设备ID阈值: {PROBLEM_USER_CONDITIONS['device_id_cnt_threshold']}, 建议调整为: {int(df_problem['device_id_cnt'].quantile(0.9))}")
        
    except Exception as e:
        print(f"调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_problem_users()
