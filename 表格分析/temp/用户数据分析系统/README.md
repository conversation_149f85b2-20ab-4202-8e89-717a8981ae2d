# 用户数据分析系统

## 📊 功能概述

用户数据分析系统是一个综合性的数据分析平台，用于分析用户注册、活动参与、问题用户识别等多维度数据，为业务决策提供数据支持。

## 🔧 核心模块

### 主程序
- **main.py** - 系统主入口，支持命令行参数配置
- **config.py** - 系统配置文件，定义各种参数和常量

### 数据处理
- **data_processor.py** - 数据处理核心模块
  - 数据清洗和预处理
  - 多表数据关联
  - 数据格式标准化

### 指标计算
- **indicator_calculator.py** - 业务指标计算模块
  - 用户注册指标
  - 活动参与度计算
  - 问题用户识别
  - KYC用户分析

### 报告生成
- **report_generator.py** - 多格式报告生成
  - CSV格式详细报告
  - Excel多工作表报告
  - 文本格式汇总报告

## 🎯 专项分析工具

### 访问渠道分析
- **most_freq_via_analyzer.py** - 问题用户访问渠道分析
- **check_most_freq_via.py** - 访问渠道数据检查工具

### 问题用户分析
- **debug_problem_users.py** - 问题用户调试和分析工具
- **super_user_detailed_analysis.py** - 超级用户详细行为分析

## 🚀 使用方法

### 基础使用
```bash
# 分析最近15天数据（默认）
python main.py

# 分析最近30天数据
python main.py --days 30

# 指定日期范围
python main.py --start_date "2025-06-01" --end_date "2025-06-30"
```

### 专项分析
```bash
# 访问渠道分析
python most_freq_via_analyzer.py --days 30

# 问题用户调试
python debug_problem_users.py

# 超级用户分析
python super_user_detailed_analysis.py
```

## 📋 输出报告

### 主要报告类型
1. **详细统计报告** (CSV格式)
   - 按日期和国家分组的详细指标
   - 包含所有计算指标和原始数据

2. **汇总报告** (文本格式)
   - 整体统计概览
   - 按国家汇总数据
   - 关键指标趋势

3. **Excel报告** (多工作表)
   - 详细统计工作表
   - 汇总统计工作表
   - 按国家汇总工作表

4. **访问渠道报告** (文本格式)
   - APP vs WEB用户分布
   - 详细渠道分类统计

## 📊 关键指标

### 用户指标
- 注册用户数
- KYC用户数和比例
- 活跃用户数
- 用户留存率

### 活动指标
- 参与活动用户数
- 活动参与率
- 奖励发放总额
- 人均奖励金额

### 风险指标
- 问题用户数和比例
- 同IP用户统计
- 同设备用户统计
- 邮箱相似用户统计
- KYC后问题用户

## ⚙️ 配置说明

### 数据源配置
- 注册用户表路径
- 问题用户表路径
- 福利用户表路径

### 分析参数
- 默认分析天数
- 问题用户判断阈值
- 输出格式配置

### 输出配置
- 报告输出路径
- 文件命名规则
- 图表生成设置

## 🔍 数据要求

### 必需数据表
1. **注册用户表** - 用户基础信息
2. **问题用户表** - 风险用户数据
3. **福利用户表** - 活动奖励数据

### 数据格式要求
- 日期格式统一
- 字段名称标准化
- 数据类型正确

## 📈 性能优化

### 数据处理优化
- 分批处理大数据集
- 内存使用优化
- 计算效率提升

### 报告生成优化
- 并行生成多种格式报告
- 增量更新机制
- 缓存机制

## 🛠️ 维护说明

### 定期维护
- 数据质量检查
- 性能监控
- 日志清理

### 扩展开发
- 新指标添加
- 新报告格式支持
- 数据源扩展
