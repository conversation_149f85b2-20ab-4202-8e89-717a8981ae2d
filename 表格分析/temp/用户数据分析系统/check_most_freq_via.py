#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np

def check_most_freq_via():
    """检查注册总表中的most_freq_via字段并统计问题用户的app和web比例"""
    
    # 读取Excel文件
    try:
        # 读取注册总表
        df_register = pd.read_excel('真实数据表格.xlsx', sheet_name='总注册用户表格')
        print("注册总表列名:")
        print(df_register.columns.tolist())
        print(f"注册总表数据量: {len(df_register)}")
        
        # 检查是否有most_freq_via字段
        if 'most_freq_via' in df_register.columns:
            print("\n发现most_freq_via字段!")
            print("most_freq_via字段的唯一值:")
            print(df_register['most_freq_via'].value_counts(dropna=False))
            
            # 读取问题用户数据
            df_problem = pd.read_excel('真实数据表格.xlsx', sheet_name='问题用户表格')
            print(f"\n问题用户数据量: {len(df_problem)}")
            
            # 合并数据，获取问题用户的most_freq_via信息
            merged_df = df_problem.merge(
                df_register[['member_id', 'most_freq_via']],
                on='member_id',
                how='left'
            )
            
            print(f"合并后数据量: {len(merged_df)}")
            print(f"有most_freq_via信息的问题用户数量: {merged_df['most_freq_via'].notna().sum()}")
            
            # 统计most_freq_via分布
            print("\n问题用户中most_freq_via分布:")
            via_counts = merged_df['most_freq_via'].value_counts(dropna=False)
            print(via_counts)
            
            # 分类统计
            # app包含ios和Android，web包含WEB和H5，空白的剔除
            app_types = ['ios', 'Android', 'IOS', 'ANDROID', 'iOS']  # 考虑大小写
            web_types = ['WEB', 'H5', 'web', 'h5']  # 考虑大小写
            
            # 过滤掉空白值
            valid_data = merged_df[merged_df['most_freq_via'].notna() & 
                                 (merged_df['most_freq_via'] != '') & 
                                 (merged_df['most_freq_via'] != 'nan')]
            
            print(f"\n有效数据量（剔除空白）: {len(valid_data)}")
            
            if len(valid_data) > 0:
                # 统计app和web
                app_count = valid_data[valid_data['most_freq_via'].isin(app_types)].shape[0]
                web_count = valid_data[valid_data['most_freq_via'].isin(web_types)].shape[0]
                other_count = len(valid_data) - app_count - web_count
                
                total_valid = len(valid_data)
                
                print(f"\n=== 问题用户most_freq_via统计结果 ===")
                print(f"总有效用户数: {total_valid}")
                print(f"APP用户数: {app_count} ({app_count/total_valid*100:.2f}%)")
                print(f"WEB用户数: {web_count} ({web_count/total_valid*100:.2f}%)")
                print(f"其他类型: {other_count} ({other_count/total_valid*100:.2f}%)")
                
                # 详细分类统计
                print(f"\n=== 详细分类统计 ===")
                for via_type in valid_data['most_freq_via'].unique():
                    count = (valid_data['most_freq_via'] == via_type).sum()
                    percentage = count / total_valid * 100
                    category = "APP" if via_type in app_types else ("WEB" if via_type in web_types else "其他")
                    print(f"{via_type}: {count}个 ({percentage:.2f}%) - {category}")
                
                # 保存结果到文件
                result_summary = f"""问题用户most_freq_via统计结果

总有效用户数: {total_valid}
APP用户数: {app_count} ({app_count/total_valid*100:.2f}%)
WEB用户数: {web_count} ({web_count/total_valid*100:.2f}%)
其他类型: {other_count} ({other_count/total_valid*100:.2f}%)

详细分类:
"""
                for via_type in valid_data['most_freq_via'].unique():
                    count = (valid_data['most_freq_via'] == via_type).sum()
                    percentage = count / total_valid * 100
                    category = "APP" if via_type in app_types else ("WEB" if via_type in web_types else "其他")
                    result_summary += f"{via_type}: {count}个 ({percentage:.2f}%) - {category}\n"
                
                with open('output/most_freq_via_analysis.txt', 'w', encoding='utf-8') as f:
                    f.write(result_summary)
                
                print(f"\n结果已保存到 output/most_freq_via_analysis.txt")
            else:
                print("没有有效的most_freq_via数据")
                
        else:
            print("注册总表中没有找到most_freq_via字段")
            
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_most_freq_via()
