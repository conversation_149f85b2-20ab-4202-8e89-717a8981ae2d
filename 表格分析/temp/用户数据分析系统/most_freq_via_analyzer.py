#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题用户访问渠道统计分析器 (most_freq_via)
专门用于统计问题用户的APP和WEB访问比例
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
from config import *
from data_processor import DataProcessor

class MostFreqViaAnalyzer:
    def __init__(self):
        self.register_df = None
        self.problem_df = None
        self.processor = None
        
    def load_data(self, days=30):
        """
        加载数据（使用与主程序相同的数据处理逻辑）
        """
        try:
            # 使用DataProcessor加载和处理数据
            self.processor = DataProcessor()

            # 加载数据
            if not self.processor.load_data(''):
                print("数据加载失败")
                return False

            # 数据清洗
            if not self.processor.clean_data():
                print("数据清洗失败")
                return False

            # 按日期范围过滤（最近N天）
            from datetime import datetime, timedelta
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')

            if not self.processor.filter_by_date_range(start_date_str, end_date_str):
                print("日期过滤失败")
                return False

            # 数据验证
            if not self.processor.validate_data():
                print("数据验证失败")
                return False

            # 获取处理后的数据
            processed_data = self.processor.get_processed_data()
            self.register_df = processed_data['register']
            self.problem_df = processed_data['problem']

            print(f"注册用户表: {len(self.register_df)} 条记录")
            print(f"问题用户表: {len(self.problem_df)} 条记录")
            print(f"分析日期范围: {start_date_str} 到 {end_date_str}")

            # 读取完整的注册总表获取most_freq_via信息
            self.register_full_df = pd.read_excel('真实数据表格.xlsx', sheet_name='总注册用户表格')

            # 检查most_freq_via字段
            if 'most_freq_via' not in self.register_full_df.columns:
                print("警告: 注册总表中没有找到most_freq_via字段")
                return False

            return True

        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def identify_problem_users(self):
        """
        识别问题用户 (复制自indicator_calculator.py的逻辑)
        """
        problem_conditions = []
        
        # 条件1: IP数量超过阈值
        condition1 = self.problem_df['ip_cnt'] > PROBLEM_USER_CONDITIONS['ip_cnt_threshold']
        
        # 条件2: 设备ID数量超过阈值
        condition2 = self.problem_df['device_id_cnt'] > PROBLEM_USER_CONDITIONS['device_id_cnt_threshold']
        
        # 条件3: 邮箱相似 - 检查YES值（不区分大小写）
        condition3 = self.problem_df['similar_email'].str.upper() == PROBLEM_USER_CONDITIONS['similar_email_flag'].upper()
        
        # 条件4: 黑灰名单 - 检查是否不为空且不为NaN
        condition4 = (self.problem_df['black_grey_flag'].notna()) & (self.problem_df['black_grey_flag'] != '') & (self.problem_df['black_grey_flag'] != 'nan')
        
        # 条件5: 风险标签非空
        condition5 = (self.problem_df['risk_label'].notna()) & (self.problem_df['risk_label'] != '') & (self.problem_df['risk_label'] != 'nan')

        # 条件6: 用户风险标识非空
        condition6 = (self.problem_df['user_risk_flag'].notna()) & (self.problem_df['user_risk_flag'] != '') & (self.problem_df['user_risk_flag'] != 'nan')
        
        # 满足任一条件即为问题用户
        problem_mask = condition1 | condition2 | condition3 | condition4 | condition5 | condition6
        
        # 返回问题用户数据
        problem_users = self.problem_df[problem_mask].copy()
        
        print(f"识别出问题用户: {len(problem_users)} 人")
        return problem_users
    
    def analyze_most_freq_via(self):
        """
        分析问题用户的most_freq_via分布
        """
        if self.register_df is None or self.problem_df is None:
            print("错误: 数据未加载")
            return None
        
        # 获取问题用户（只获取被识别为问题用户的记录）
        problem_users = self.identify_problem_users()
        print(f"识别出的问题用户数量: {len(problem_users)}")

        # 合并数据，获取问题用户的most_freq_via信息（使用完整的注册表）
        merged_df = problem_users.merge(
            self.register_full_df[['member_id', 'most_freq_via']],
            on='member_id',
            how='left'
        )

        print(f"合并后的问题用户数量: {len(merged_df)}")
        print(f"有most_freq_via信息的问题用户数量: {merged_df['most_freq_via'].notna().sum()}")
        
        # 过滤掉空白值
        valid_data = merged_df[
            merged_df['most_freq_via'].notna() & 
            (merged_df['most_freq_via'] != '') & 
            (merged_df['most_freq_via'] != 'nan')
        ]
        
        print(f"有效数据量（剔除空白）: {len(valid_data)}")
        
        if len(valid_data) == 0:
            print("没有有效的most_freq_via数据")
            return None
        
        # 分类统计
        # app包含ios和Android，web包含WEB和H5
        app_types = ['ios', 'Android', 'IOS', 'ANDROID', 'iOS']
        web_types = ['WEB', 'H5', 'web', 'h5']
        
        # 统计app和web
        app_count = valid_data[valid_data['most_freq_via'].isin(app_types)].shape[0]
        web_count = valid_data[valid_data['most_freq_via'].isin(web_types)].shape[0]
        other_count = len(valid_data) - app_count - web_count
        
        total_valid = len(valid_data)
        
        # 详细分类统计
        detailed_stats = {}
        for via_type in valid_data['most_freq_via'].unique():
            count = (valid_data['most_freq_via'] == via_type).sum()
            percentage = count / total_valid * 100
            category = "APP" if via_type in app_types else ("WEB" if via_type in web_types else "其他")
            detailed_stats[via_type] = {
                'count': count,
                'percentage': percentage,
                'category': category
            }
        
        return {
            'total_valid_users': total_valid,
            'app_users': app_count,
            'web_users': web_count,
            'other_users': other_count,
            'app_percentage': round(app_count / total_valid * 100, 2),
            'web_percentage': round(web_count / total_valid * 100, 2),
            'other_percentage': round(other_count / total_valid * 100, 2),
            'detailed_stats': detailed_stats,
            'raw_data': merged_df
        }
    
    def print_analysis_report(self, stats):
        """
        打印分析报告
        """
        if stats is None:
            print("没有统计数据")
            return
        
        print("\n" + "=" * 60)
        print("问题用户访问渠道统计 (most_freq_via)")
        print("=" * 60)
        
        # 打印总体统计
        print(f"\n总体统计:")
        print("-" * 40)
        print(f"总有效用户数: {stats['total_valid_users']:,}")
        print(f"APP用户数: {stats['app_users']:,} ({stats['app_percentage']:.2f}%)")
        print(f"WEB用户数: {stats['web_users']:,} ({stats['web_percentage']:.2f}%)")
        if stats.get('other_users', 0) > 0:
            print(f"其他类型: {stats['other_users']:,} ({stats['other_percentage']:.2f}%)")
        
        # 打印详细分类统计
        print(f"\n详细分类统计:")
        print("-" * 40)
        detailed_stats = stats['detailed_stats']
        
        # 按数量排序
        sorted_stats = sorted(detailed_stats.items(), key=lambda x: x[1]['count'], reverse=True)
        
        for via_type, stat in sorted_stats:
            category_icon = "📱" if stat['category'] == "APP" else ("🌐" if stat['category'] == "WEB" else "❓")
            print(f"{category_icon} {via_type}: {stat['count']:,}个 ({stat['percentage']:.2f}%) - {stat['category']}")
    
    def save_analysis_report(self, stats):
        """
        保存分析报告到文件
        """
        if stats is None:
            return None
        
        try:
            os.makedirs(OUTPUT_DIR, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = os.path.join(OUTPUT_DIR, f"most_freq_via_analysis_{timestamp}.txt")
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("问题用户访问渠道统计 (most_freq_via)\n")
                f.write("=" * 60 + "\n\n")
                
                f.write("总体统计:\n")
                f.write("-" * 40 + "\n")
                f.write(f"总有效用户数: {stats['total_valid_users']:,}\n")
                f.write(f"APP用户数: {stats['app_users']:,} ({stats['app_percentage']:.2f}%)\n")
                f.write(f"WEB用户数: {stats['web_users']:,} ({stats['web_percentage']:.2f}%)\n")
                if stats.get('other_users', 0) > 0:
                    f.write(f"其他类型: {stats['other_users']:,} ({stats['other_percentage']:.2f}%)\n")
                
                f.write(f"\n详细分类统计:\n")
                f.write("-" * 40 + "\n")
                
                detailed_stats = stats['detailed_stats']
                sorted_stats = sorted(detailed_stats.items(), key=lambda x: x[1]['count'], reverse=True)
                
                for via_type, stat in sorted_stats:
                    f.write(f"{via_type}: {stat['count']:,}个 ({stat['percentage']:.2f}%) - {stat['category']}\n")
            
            print(f"\n分析报告已保存到: {output_path}")
            return output_path
            
        except Exception as e:
            print(f"保存分析报告失败: {e}")
            return None

def main():
    """
    主函数
    """
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='问题用户访问渠道统计分析器')
    parser.add_argument('--days', type=int, default=30, help='分析最近N天的数据 (默认30天)')
    args = parser.parse_args()

    print("=" * 60)
    print("问题用户访问渠道统计分析器")
    print("=" * 60)

    analyzer = MostFreqViaAnalyzer()

    # 加载数据
    if not analyzer.load_data(days=args.days):
        print("数据加载失败，程序退出")
        return False
    
    # 分析most_freq_via
    stats = analyzer.analyze_most_freq_via()
    
    if stats is None:
        print("分析失败，程序退出")
        return False
    
    # 打印报告
    analyzer.print_analysis_report(stats)
    
    # 保存报告
    analyzer.save_analysis_report(stats)
    
    print("\n" + "=" * 60)
    print("分析完成!")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
