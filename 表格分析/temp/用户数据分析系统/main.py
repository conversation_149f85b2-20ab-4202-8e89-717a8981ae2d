# -*- coding: utf-8 -*-
"""
用户数据分析系统主程序
"""

import argparse
import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_processor import DataProcessor
from indicator_calculator import IndicatorCalculator
from report_generator import ReportGenerator
from config import *

def parse_arguments():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description='用户数据分析系统')
    
    parser.add_argument('--start_date', 
                       type=str, 
                       help='分析开始日期 (格式: YYYY-MM-DD)',
                       default=None)
    
    parser.add_argument('--end_date', 
                       type=str, 
                       help='分析结束日期 (格式: YYYY-MM-DD)',
                       default=None)
    
    parser.add_argument('--data_path', 
                       type=str, 
                       help='数据文件路径',
                       default='')
    
    parser.add_argument('--output_path', 
                       type=str, 
                       help='输出文件路径',
                       default=None)
    
    parser.add_argument('--days', 
                       type=int, 
                       help='分析最近N天的数据 (默认15天)',
                       default=15)
    
    return parser.parse_args()

def get_default_date_range(days=15):
    """
    获取默认的日期范围 (最近N天)
    """
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days-1)
    
    return start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')

def main():
    """
    主函数
    """
    print("=" * 60)
    print("用户数据分析系统")
    print("=" * 60)
    
    # 解析命令行参数
    args = parse_arguments()
    
    # 确定分析日期范围
    if args.start_date and args.end_date:
        start_date = args.start_date
        end_date = args.end_date
    else:
        start_date, end_date = get_default_date_range(args.days)
        print(f"使用默认日期范围: {start_date} 到 {end_date}")
    
    try:
        # 步骤1: 数据处理
        print("\n步骤1: 数据加载和处理")
        print("-" * 40)
        
        processor = DataProcessor()
        
        # 加载数据
        if not processor.load_data(args.data_path):
            print("数据加载失败，程序退出")
            return False
        
        # 数据清洗
        if not processor.clean_data():
            print("数据清洗失败，程序退出")
            return False
        
        # 按日期范围过滤
        if not processor.filter_by_date_range(start_date, end_date):
            print("日期过滤失败，程序退出")
            return False
        
        # 数据验证
        if not processor.validate_data():
            print("数据验证失败，程序退出")
            return False
        
        # 步骤2: 指标计算
        print("\n步骤2: 指标计算")
        print("-" * 40)
        
        calculator = IndicatorCalculator(processor.get_processed_data())
        
        # 识别问题用户
        problem_users = calculator.identify_problem_users()
        print(f"识别出问题用户: {len(problem_users)} 人")
        
        # 计算各项指标
        results_df = calculator.calculate_daily_country_indicators()
        print(f"计算完成，共 {len(results_df)} 条统计记录")
        
        # 获取汇总统计
        summary_stats = calculator.get_summary_statistics()
        
        # 步骤3: 报告生成
        print("\n步骤3: 报告生成")
        print("-" * 40)
        
        generator = ReportGenerator()
        generator.set_results(results_df, summary_stats)
        
        # 验证结果
        if not generator.validate_results():
            print("结果验证失败，程序退出")
            return False
        
        # 生成报告
        csv_path = generator.generate_csv_report(args.output_path)
        summary_path = generator.generate_summary_report()
        
        # 尝试生成Excel报告
        excel_path = generator.export_excel_report()
        
        # 在控制台显示结果
        generator.print_console_report()

        # 显示most_freq_via统计
        generator.print_most_freq_via_report(calculator)
        
        print("\n" + "=" * 60)
        print("分析完成!")
        print("=" * 60)
        print(f"分析时间范围: {start_date} 到 {end_date}")
        print(f"CSV报告: {csv_path}")
        print(f"汇总报告: {summary_path}")
        if excel_path:
            print(f"Excel报告: {excel_path}")
        
        return True
        
    except Exception as e:
        print(f"\n程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_sample_data():
    """
    创建示例数据文件 (用于测试)
    """
    print("创建示例数据文件...")
    
    # 创建数据目录
    data_dir = os.path.join(os.path.dirname(__file__), 'data')
    os.makedirs(data_dir, exist_ok=True)
    
    # 示例注册用户数据
    register_data = {
        'member_id': ['U001', 'U002', 'U003', 'U004', 'U005'],
        'register_time': ['2025/6/19 0:07:23', '2025/6/19 1:15:30', '2025/6/20 2:20:15', 
                         '2025/6/20 3:30:45', '2025/6/21 4:45:12'],
        'member_country': ['CN', 'US', 'CN', 'JP', 'US'],
        'kyc_level': [1, 3, 3, 1, 3]
    }
    
    # 示例问题用户数据
    problem_data = {
        'member_id': ['U001', 'U003', 'U005'],
        'agent_flag': ['N', 'N', 'Y'],
        'member_country': ['CN', 'CN', 'US'],
        'register_time': ['2025/6/19 0:07:23', '2025/6/20 2:20:15', '2025/6/21 4:45:12'],
        'ip_cnt': [6, 3, 2],
        'device_id_cnt': [1, 3, 1],
        'black_grey_flag': ['', '黑名单', ''],
        'risk_label': ['高风险', '', ''],
        'create_time': ['2025/6/19 10:00:00', '2025/6/20 11:00:00', '2025/6/21 12:00:00'],
        'similar_email': ['no', 'yes', 'no'],
        'user_risk_flag': ['', '', '风险用户'],
        'dt': ['2025-06-19', '2025-06-20', '2025-06-21']
    }
    
    # 示例福利用户数据
    welfare_data = {
        'member_id': ['U002', 'U003', 'U004'],
        'newuser_bonus': [100.0, 50.0, 200.0],
        'POSITION_bonus': [50.0, 25.0, 100.0],
        'register_time': ['2025/6/19 1:15:30', '2025/6/20 2:20:15', '2025/6/20 3:30:45'],
        'dt': ['2025-06-19', '2025-06-20', '2025-06-20']
    }
    
    # 保存示例数据
    import pandas as pd
    
    pd.DataFrame(register_data).to_csv(
        os.path.join(data_dir, 'register_users.csv'), 
        index=False, encoding='utf-8-sig'
    )
    
    pd.DataFrame(problem_data).to_csv(
        os.path.join(data_dir, 'problem_users.csv'), 
        index=False, encoding='utf-8-sig'
    )
    
    pd.DataFrame(welfare_data).to_csv(
        os.path.join(data_dir, 'welfare_users.csv'), 
        index=False, encoding='utf-8-sig'
    )
    
    print(f"示例数据已创建在: {data_dir}")

if __name__ == "__main__":
    # 检查是否需要创建示例数据
    if len(sys.argv) > 1 and sys.argv[1] == '--create-sample':
        create_sample_data()
    else:
        success = main()
        sys.exit(0 if success else 1)
