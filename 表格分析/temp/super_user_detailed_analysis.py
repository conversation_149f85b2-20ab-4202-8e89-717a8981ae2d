#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超高频用户详细分析脚本
专门分析有10次以上交易超过20小时的用户的详细交易行为
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

def load_and_analyze_super_users():
    """加载数据并分析超高频用户"""
    print("正在加载数据...")
    
    # 加载原始数据
    df = pd.read_csv("data/近3个月高频交易分析.csv")
    df['create_time'] = pd.to_datetime(df['create_time'])
    df['date'] = df['create_time'].dt.date
    df['hour'] = df['create_time'].dt.hour
    
    print(f"数据加载完成，共 {len(df)} 条记录")
    
    # 找出超高频用户
    daily_hours = df.groupby(['member_id', 'date'])['hour'].nunique().reset_index()
    daily_hours.columns = ['member_id', 'date', 'trading_hours']
    
    high_freq_users = daily_hours[daily_hours['trading_hours'] > 20].copy()
    user_high_freq_days = high_freq_users.groupby('member_id').size().reset_index()
    user_high_freq_days.columns = ['member_id', 'high_freq_days_count']
    
    # 筛选出有10次以上的超高频用户
    super_users = user_high_freq_days[user_high_freq_days['high_freq_days_count'] >= 10]['member_id'].tolist()
    super_user_data = df[df['member_id'].isin(super_users)].copy()
    
    print(f"超高频用户数量: {len(super_users)}")
    print(f"超高频用户交易记录数: {len(super_user_data):,}")
    
    return super_user_data, super_users, user_high_freq_days

def analyze_coin_details(super_user_data):
    """详细分析币种交易情况"""
    print("\n=== 币种详细分析 ===")
    
    # 按币种统计
    coin_stats = super_user_data.groupby('contract_name').agg({
        'member_id': ['count', 'nunique'],  # 交易次数和交易用户数
        'deal_vol_usdt': ['sum', 'mean'],   # 总交易量和平均交易量
        'profit': ['sum', 'mean', 'count']  # 总盈利、平均盈利、盈利交易次数
    }).reset_index()
    
    # 扁平化列名
    coin_stats.columns = ['contract_name', 'total_trades', 'unique_users', 
                         'total_volume', 'avg_volume_per_trade',
                         'total_profit', 'avg_profit_per_trade', 'profit_trades']
    
    # 计算盈利率和用户参与度
    coin_stats['profit_rate'] = (coin_stats['total_profit'] / coin_stats['total_volume'] * 100).fillna(0)
    coin_stats['user_participation_rate'] = coin_stats['unique_users'] / len(super_user_data['member_id'].unique()) * 100
    
    # 按交易次数排序
    coin_stats = coin_stats.sort_values('total_trades', ascending=False)
    
    print("热门币种 TOP 20:")
    print("-" * 120)
    print(f"{'币种':<20} {'交易次数':<10} {'用户数':<8} {'总交易量(USDT)':<18} {'总盈利(USDT)':<15} {'盈利率(%)':<10} {'用户参与率(%)':<12}")
    print("-" * 120)
    
    for i, row in coin_stats.head(20).iterrows():
        print(f"{row['contract_name']:<20} {row['total_trades']:<10,} {row['unique_users']:<8} "
              f"{row['total_volume']:<18,.2f} {row['total_profit']:<15,.2f} "
              f"{row['profit_rate']:<10.4f} {row['user_participation_rate']:<12.2f}")
    
    return coin_stats

def analyze_user_profit_details(super_user_data, super_users):
    """详细分析用户盈利情况"""
    print("\n=== 用户盈利详细分析 ===")
    
    user_stats = []
    
    for user_id in super_users:
        user_data = super_user_data[super_user_data['member_id'] == user_id]
        
        # 基础统计
        total_trades = len(user_data)
        total_profit = user_data['profit'].sum()
        total_volume = user_data['deal_vol_usdt'].sum()
        profit_rate = (total_profit / total_volume * 100) if total_volume > 0 else 0
        
        # 盈利交易统计
        profitable_trades = len(user_data[user_data['profit'] > 0])
        loss_trades = len(user_data[user_data['profit'] < 0])
        win_rate = (profitable_trades / total_trades * 100) if total_trades > 0 else 0
        
        # 交易币种数
        unique_coins = user_data['contract_name'].nunique()
        
        # 最大单笔盈利和亏损
        max_profit = user_data['profit'].max()
        max_loss = user_data['profit'].min()
        
        # 平均每日交易量
        trading_days = user_data['date'].nunique()
        avg_daily_trades = total_trades / trading_days if trading_days > 0 else 0
        
        user_stats.append({
            'member_id': user_id,
            'total_trades': total_trades,
            'total_profit': total_profit,
            'total_volume': total_volume,
            'profit_rate': profit_rate,
            'win_rate': win_rate,
            'unique_coins': unique_coins,
            'max_profit': max_profit,
            'max_loss': max_loss,
            'trading_days': trading_days,
            'avg_daily_trades': avg_daily_trades
        })
    
    user_df = pd.DataFrame(user_stats)
    user_df = user_df.sort_values('total_profit', ascending=False)
    
    print("用户盈利排行 TOP 15:")
    print("-" * 140)
    print(f"{'用户ID':<12} {'总交易':<8} {'总盈利(USDT)':<15} {'盈利率(%)':<10} {'胜率(%)':<8} "
          f"{'币种数':<8} {'交易天数':<8} {'日均交易':<10}")
    print("-" * 140)
    
    for i, row in user_df.head(15).iterrows():
        print(f"{row['member_id'][:8]}*** {row['total_trades']:<8,} {row['total_profit']:<15,.2f} "
              f"{row['profit_rate']:<10.4f} {row['win_rate']:<8.2f} {row['unique_coins']:<8} "
              f"{row['trading_days']:<8} {row['avg_daily_trades']:<10.1f}")
    
    return user_df

def analyze_trading_patterns(super_user_data):
    """分析交易模式"""
    print("\n=== 交易模式分析 ===")
    
    # 按小时分析交易分布
    hourly_stats = super_user_data.groupby('hour').agg({
        'member_id': 'count',
        'deal_vol_usdt': 'sum',
        'profit': 'sum'
    }).reset_index()
    hourly_stats.columns = ['hour', 'trade_count', 'volume', 'profit']
    
    print("24小时交易分布:")
    print("-" * 60)
    print(f"{'小时':<6} {'交易次数':<12} {'交易量(USDT)':<18} {'盈利(USDT)':<15}")
    print("-" * 60)
    
    for i, row in hourly_stats.iterrows():
        print(f"{int(row['hour']):02d}:00 {row['trade_count']:<12,} {row['volume']:<18,.2f} {row['profit']:<15,.2f}")
    
    # 分析最活跃的交易时段
    peak_hours = hourly_stats.nlargest(5, 'trade_count')
    print(f"\n最活跃的5个交易小时:")
    for i, row in peak_hours.iterrows():
        print(f"- {int(row['hour']):02d}:00 - {row['trade_count']:,}次交易")
    
    return hourly_stats

def generate_comprehensive_report(coin_stats, user_df, hourly_stats, super_user_data):
    """生成综合分析报告"""
    print("\n正在生成综合分析报告...")
    
    report = []
    report.append("# 超高频交易用户综合深度分析报告")
    report.append("=" * 80)
    report.append("")
    
    # 执行摘要
    total_users = len(user_df)
    total_profit = super_user_data['profit'].sum()
    total_volume = super_user_data['deal_vol_usdt'].sum()
    total_trades = len(super_user_data)
    
    report.append("## 执行摘要")
    report.append(f"- 分析用户数: {total_users}")
    report.append(f"- 总交易次数: {total_trades:,}")
    report.append(f"- 总交易量: ${total_volume:,.2f}")
    report.append(f"- 总盈利: ${total_profit:,.2f}")
    report.append(f"- 整体盈利率: {(total_profit/total_volume*100):.4f}%")
    report.append("")
    
    # 币种分析摘要
    report.append("## 币种分析关键发现")
    top_coin = coin_stats.iloc[0]
    most_profitable_coin = coin_stats.loc[coin_stats['total_profit'].idxmax()]
    
    report.append(f"- 最热门币种: {top_coin['contract_name']} ({top_coin['total_trades']:,}次交易)")
    report.append(f"- 最盈利币种: {most_profitable_coin['contract_name']} (${most_profitable_coin['total_profit']:,.2f})")
    report.append(f"- 交易币种总数: {len(coin_stats)}")
    report.append("")
    
    # 用户分析摘要
    profitable_users = len(user_df[user_df['total_profit'] > 0])
    loss_users = len(user_df[user_df['total_profit'] < 0])
    top_trader = user_df.iloc[0]
    
    report.append("## 用户分析关键发现")
    report.append(f"- 盈利用户: {profitable_users} ({profitable_users/total_users*100:.1f}%)")
    report.append(f"- 亏损用户: {loss_users} ({loss_users/total_users*100:.1f}%)")
    report.append(f"- 最佳交易者: {top_trader['member_id'][:8]}*** (${top_trader['total_profit']:,.2f})")
    report.append(f"- 平均胜率: {user_df['win_rate'].mean():.2f}%")
    report.append("")
    
    # 交易时间分析
    peak_hour = hourly_stats.loc[hourly_stats['trade_count'].idxmax()]
    report.append("## 交易时间分析")
    report.append(f"- 最活跃时段: {int(peak_hour['hour']):02d}:00 ({peak_hour['trade_count']:,}次交易)")
    report.append(f"- 24小时平均交易: {hourly_stats['trade_count'].mean():.0f}次/小时")
    report.append("")
    
    # 保存报告
    with open('超高频用户综合分析报告.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("综合分析报告已保存到: 超高频用户综合分析报告.txt")

def main():
    """主函数"""
    # 加载数据
    super_user_data, super_users, user_high_freq_days = load_and_analyze_super_users()
    
    # 详细分析
    coin_stats = analyze_coin_details(super_user_data)
    user_df = analyze_user_profit_details(super_user_data, super_users)
    hourly_stats = analyze_trading_patterns(super_user_data)
    
    # 生成综合报告
    generate_comprehensive_report(coin_stats, user_df, hourly_stats, super_user_data)
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
