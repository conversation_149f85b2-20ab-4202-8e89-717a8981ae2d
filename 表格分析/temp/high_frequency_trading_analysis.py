#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高频交易分析脚本
分析最近6个月的合约交易用户中，有3次及以上，每天连续20个小时有交易记录的用户
关注create_time统计每天大于20小时的交易条数
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# 尝试导入matplotlib，如果失败则跳过可视化
try:
    import matplotlib.pyplot as plt
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    print("警告: matplotlib未安装，将跳过可视化功能")

class HighFrequencyTradingAnalyzer:
    def __init__(self, csv_file_path):
        """初始化分析器"""
        self.csv_file_path = csv_file_path
        self.df = None
        self.analysis_results = {}
        
    def load_data(self):
        """加载CSV数据"""
        print("正在加载数据...")
        try:
            self.df = pd.read_csv(self.csv_file_path)
            print(f"数据加载完成，共 {len(self.df)} 条记录")
            print(f"数据列: {list(self.df.columns)}")
            
            # 转换时间格式
            self.df['create_time'] = pd.to_datetime(self.df['create_time'])
            self.df['date'] = self.df['create_time'].dt.date
            self.df['hour'] = self.df['create_time'].dt.hour
            
            print(f"时间范围: {self.df['create_time'].min()} 到 {self.df['create_time'].max()}")
            print(f"唯一用户数: {self.df['member_id'].nunique()}")
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
        return True
    
    def analyze_daily_trading_hours(self):
        """分析每个用户每天的交易小时数"""
        print("正在分析每日交易小时数...")
        
        # 按用户和日期分组，计算每天的交易小时数
        daily_hours = self.df.groupby(['member_id', 'date'])['hour'].nunique().reset_index()
        daily_hours.columns = ['member_id', 'date', 'trading_hours']
        
        # 筛选出每天交易超过20小时的记录
        high_frequency_daily = daily_hours[daily_hours['trading_hours'] > 20].copy()
        
        print(f"每天交易超过20小时的记录数: {len(high_frequency_daily)}")
        
        return daily_hours, high_frequency_daily
    
    def find_qualified_users(self, daily_hours):
        """找出符合条件的用户：有3次及以上每天连续20个小时有交易记录"""
        print("正在寻找符合条件的用户...")
        
        # 筛选出每天交易超过20小时的用户
        high_freq_users = daily_hours[daily_hours['trading_hours'] > 20].copy()
        
        # 统计每个用户有多少天交易超过20小时
        user_high_freq_days = high_freq_users.groupby('member_id').size().reset_index()
        user_high_freq_days.columns = ['member_id', 'high_freq_days_count']
        
        # 筛选出有3次及以上的用户
        qualified_users = user_high_freq_days[user_high_freq_days['high_freq_days_count'] >= 3]['member_id'].tolist()
        
        print(f"符合条件的用户数量: {len(qualified_users)}")
        
        return qualified_users, user_high_freq_days
    
    def analyze_daily_statistics(self, qualified_users):
        """分析每天大于20小时的交易条数统计"""
        print("正在分析每日统计数据...")
        
        # 筛选符合条件用户的数据
        qualified_data = self.df[self.df['member_id'].isin(qualified_users)].copy()
        
        # 按用户和日期分组，计算每天的交易小时数和交易条数
        daily_stats = qualified_data.groupby(['member_id', 'date']).agg({
            'hour': 'nunique',  # 交易小时数
            'create_time': 'count'  # 交易条数
        }).reset_index()
        daily_stats.columns = ['member_id', 'date', 'trading_hours', 'transaction_count']
        
        # 筛选出每天交易超过20小时的记录
        daily_over_20h = daily_stats[daily_stats['trading_hours'] > 20].copy()
        
        # 按日期统计每天大于20小时的交易条数
        daily_summary = daily_over_20h.groupby('date').agg({
            'transaction_count': 'sum',  # 总交易条数
            'member_id': 'count'  # 用户数量
        }).reset_index()
        daily_summary.columns = ['date', 'total_transactions', 'user_count']
        
        return daily_over_20h, daily_summary
    
    def generate_analysis_report(self):
        """生成完整的分析报告"""
        print("开始生成分析报告...")
        
        if not self.load_data():
            return
        
        # 1. 分析每日交易小时数
        daily_hours, high_frequency_daily = self.analyze_daily_trading_hours()
        
        # 2. 找出符合条件的用户
        qualified_users, user_high_freq_days = self.find_qualified_users(daily_hours)
        
        # 3. 分析每日统计数据
        daily_over_20h, daily_summary = self.analyze_daily_statistics(qualified_users)
        
        # 保存结果
        self.analysis_results = {
            'daily_hours': daily_hours,
            'high_frequency_daily': high_frequency_daily,
            'qualified_users': qualified_users,
            'user_high_freq_days': user_high_freq_days,
            'daily_over_20h': daily_over_20h,
            'daily_summary': daily_summary
        }
        
        # 生成报告
        self.create_detailed_report()
        self.create_visualizations()
        
        print("分析报告生成完成！")
    
    def create_detailed_report(self):
        """创建详细的文字报告"""
        results = self.analysis_results
        
        report = []
        report.append("# 高频交易用户分析报告")
        report.append("=" * 50)
        report.append("")
        
        # 基础统计
        report.append("## 1. 基础数据统计")
        report.append(f"- 总交易记录数: {len(self.df):,}")
        report.append(f"- 总用户数: {self.df['member_id'].nunique():,}")
        report.append(f"- 数据时间范围: {self.df['create_time'].min()} 到 {self.df['create_time'].max()}")
        report.append("")
        
        # 高频交易用户统计
        report.append("## 2. 高频交易用户统计")
        report.append(f"- 每天交易超过20小时的记录数: {len(results['high_frequency_daily']):,}")
        report.append(f"- 符合条件的用户数量（3次及以上每天20+小时交易）: {len(results['qualified_users']):,}")
        report.append("")
        
        # 用户分布统计
        user_dist = results['user_high_freq_days']['high_freq_days_count'].describe()
        report.append("## 3. 用户高频交易天数分布")
        report.append(f"- 平均高频交易天数: {user_dist['mean']:.2f}")
        report.append(f"- 最大高频交易天数: {int(user_dist['max'])}")
        report.append(f"- 最小高频交易天数: {int(user_dist['min'])}")
        report.append("")
        
        # 每日统计概览
        if len(results['daily_summary']) > 0:
            daily_stats = results['daily_summary']
            report.append("## 4. 每日交易统计概览")
            report.append(f"- 统计天数: {len(daily_stats)}")
            report.append(f"- 日均交易条数: {daily_stats['total_transactions'].mean():.0f}")
            report.append(f"- 日均活跃用户数: {daily_stats['user_count'].mean():.1f}")
            report.append(f"- 最高单日交易条数: {daily_stats['total_transactions'].max():,}")
            report.append(f"- 最高单日活跃用户数: {daily_stats['user_count'].max()}")
            report.append("")
        
        # 保存报告
        with open('高频交易分析报告.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))

        print("详细报告已保存到: 高频交易分析报告.txt")

    def create_visualizations(self):
        """创建可视化图表"""
        if not HAS_MATPLOTLIB:
            print("跳过可视化功能（matplotlib未安装）")
            return

        results = self.analysis_results

        # 创建图表目录
        import os
        os.makedirs('charts', exist_ok=True)

        # 1. 每日交易条数趋势图
        if len(results['daily_summary']) > 0:
            plt.figure(figsize=(15, 8))

            # 转换日期格式
            daily_summary = results['daily_summary'].copy()
            daily_summary['date'] = pd.to_datetime(daily_summary['date'])
            daily_summary = daily_summary.sort_values('date')

            # 创建子图
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))

            # 每日交易条数
            ax1.plot(daily_summary['date'], daily_summary['total_transactions'],
                    marker='o', linewidth=2, markersize=4)
            ax1.set_title('每日高频交易条数趋势', fontsize=16, fontweight='bold')
            ax1.set_xlabel('日期', fontsize=12)
            ax1.set_ylabel('交易条数', fontsize=12)
            ax1.grid(True, alpha=0.3)
            ax1.tick_params(axis='x', rotation=45)

            # 每日活跃用户数
            ax2.plot(daily_summary['date'], daily_summary['user_count'],
                    marker='s', linewidth=2, markersize=4, color='orange')
            ax2.set_title('每日高频交易活跃用户数趋势', fontsize=16, fontweight='bold')
            ax2.set_xlabel('日期', fontsize=12)
            ax2.set_ylabel('用户数', fontsize=12)
            ax2.grid(True, alpha=0.3)
            ax2.tick_params(axis='x', rotation=45)

            plt.tight_layout()
            plt.savefig('charts/daily_trends.png', dpi=300, bbox_inches='tight')
            plt.close()

        # 2. 用户高频交易天数分布
        plt.figure(figsize=(12, 8))
        user_dist = results['user_high_freq_days']['high_freq_days_count']

        # 直方图
        plt.subplot(2, 2, 1)
        plt.hist(user_dist, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.title('用户高频交易天数分布', fontsize=14, fontweight='bold')
        plt.xlabel('高频交易天数')
        plt.ylabel('用户数量')
        plt.grid(True, alpha=0.3)

        # 箱线图
        plt.subplot(2, 2, 2)
        plt.boxplot(user_dist)
        plt.title('用户高频交易天数箱线图', fontsize=14, fontweight='bold')
        plt.ylabel('高频交易天数')
        plt.grid(True, alpha=0.3)

        # 累积分布
        plt.subplot(2, 2, 3)
        sorted_data = np.sort(user_dist)
        cumulative = np.arange(1, len(sorted_data) + 1) / len(sorted_data)
        plt.plot(sorted_data, cumulative, marker='o', markersize=3)
        plt.title('用户高频交易天数累积分布', fontsize=14, fontweight='bold')
        plt.xlabel('高频交易天数')
        plt.ylabel('累积概率')
        plt.grid(True, alpha=0.3)

        # 统计表格
        plt.subplot(2, 2, 4)
        plt.axis('off')
        stats_text = f"""
        统计摘要:
        总用户数: {len(user_dist):,}
        平均天数: {user_dist.mean():.2f}
        中位数: {user_dist.median():.2f}
        标准差: {user_dist.std():.2f}
        最小值: {user_dist.min()}
        最大值: {user_dist.max()}
        """
        plt.text(0.1, 0.5, stats_text, fontsize=12, verticalalignment='center')

        plt.tight_layout()
        plt.savefig('charts/user_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("可视化图表已保存到: charts/")

    def export_detailed_data(self):
        """导出详细数据到Excel"""
        results = self.analysis_results

        with pd.ExcelWriter('高频交易详细数据.xlsx', engine='openpyxl') as writer:
            # 每日统计汇总
            if len(results['daily_summary']) > 0:
                results['daily_summary'].to_excel(writer, sheet_name='每日统计汇总', index=False)

            # 符合条件的用户列表
            if len(results['qualified_users']) > 0:
                qualified_df = pd.DataFrame({
                    'member_id': results['qualified_users']
                })
                qualified_df.to_excel(writer, sheet_name='符合条件用户列表', index=False)

            # 用户高频交易天数统计
            results['user_high_freq_days'].to_excel(writer, sheet_name='用户高频天数统计', index=False)

            # 每日超20小时交易详情（前1000条）
            if len(results['daily_over_20h']) > 0:
                sample_data = results['daily_over_20h'].head(1000)
                sample_data.to_excel(writer, sheet_name='每日超20小时详情样本', index=False)

        print("详细数据已导出到: 高频交易详细数据.xlsx")

    def analyze_super_high_frequency_users(self):
        """分析有10次以上交易超过20小时的超高频用户"""
        print("正在分析超高频用户（10次以上每天20+小时交易）...")

        results = self.analysis_results
        user_high_freq_days = results['user_high_freq_days']

        # 筛选出有10次以上的超高频用户
        super_users = user_high_freq_days[user_high_freq_days['high_freq_days_count'] >= 10]['member_id'].tolist()
        print(f"超高频用户数量: {len(super_users)}")

        if len(super_users) == 0:
            print("没有找到符合条件的超高频用户")
            return

        # 获取这些用户的所有交易数据
        super_user_data = self.df[self.df['member_id'].isin(super_users)].copy()
        print(f"超高频用户总交易记录数: {len(super_user_data):,}")

        # 1. 分析交易币种分布
        coin_analysis = self.analyze_coin_distribution(super_user_data, super_users)

        # 2. 分析盈利情况
        profit_analysis = self.analyze_profit_distribution(super_user_data, super_users)

        # 3. 生成超高频用户报告
        self.generate_super_user_report(super_users, coin_analysis, profit_analysis)

        return super_users, coin_analysis, profit_analysis

    def analyze_coin_distribution(self, super_user_data, super_users):
        """分析超高频用户的币种分布"""
        print("正在分析币种分布...")

        # 按币种统计交易次数和交易量
        coin_stats = super_user_data.groupby('contract_name').agg({
            'member_id': 'count',  # 交易次数
            'deal_vol_usdt': 'sum',  # 总交易量
            'profit': 'sum'  # 总盈利
        }).reset_index()
        coin_stats.columns = ['contract_name', 'transaction_count', 'total_volume_usdt', 'total_profit']

        # 计算占比
        total_transactions = coin_stats['transaction_count'].sum()
        total_volume = coin_stats['total_volume_usdt'].sum()

        coin_stats['transaction_ratio'] = coin_stats['transaction_count'] / total_transactions * 100
        coin_stats['volume_ratio'] = coin_stats['total_volume_usdt'] / total_volume * 100

        # 按交易次数排序
        coin_stats = coin_stats.sort_values('transaction_count', ascending=False)

        # 分析每个用户的币种偏好
        user_coin_preference = {}
        for user_id in super_users:
            user_data = super_user_data[super_user_data['member_id'] == user_id]
            user_coins = user_data.groupby('contract_name').agg({
                'member_id': 'count',
                'deal_vol_usdt': 'sum',
                'profit': 'sum'
            }).reset_index()
            user_coins.columns = ['contract_name', 'count', 'volume', 'profit']
            user_coins = user_coins.sort_values('count', ascending=False)
            user_coin_preference[user_id] = user_coins

        return {
            'overall_stats': coin_stats,
            'user_preferences': user_coin_preference
        }

    def analyze_profit_distribution(self, super_user_data, super_users):
        """分析超高频用户的盈利分布"""
        print("正在分析盈利分布...")

        # 按用户统计盈利情况
        user_profit_stats = super_user_data.groupby('member_id').agg({
            'profit': ['sum', 'mean', 'count'],
            'deal_vol_usdt': 'sum'
        }).reset_index()

        # 扁平化列名
        user_profit_stats.columns = ['member_id', 'total_profit', 'avg_profit_per_trade', 'trade_count', 'total_volume']

        # 计算盈利率
        user_profit_stats['profit_rate'] = (user_profit_stats['total_profit'] / user_profit_stats['total_volume'] * 100).fillna(0)

        # 按总盈利排序
        user_profit_stats = user_profit_stats.sort_values('total_profit', ascending=False)

        # 整体统计
        total_profit = super_user_data['profit'].sum()
        total_volume = super_user_data['deal_vol_usdt'].sum()
        overall_profit_rate = total_profit / total_volume * 100 if total_volume > 0 else 0

        profitable_users = len(user_profit_stats[user_profit_stats['total_profit'] > 0])
        loss_users = len(user_profit_stats[user_profit_stats['total_profit'] < 0])

        return {
            'user_stats': user_profit_stats,
            'total_profit': total_profit,
            'total_volume': total_volume,
            'overall_profit_rate': overall_profit_rate,
            'profitable_users': profitable_users,
            'loss_users': loss_users
        }

    def generate_super_user_report(self, super_users, coin_analysis, profit_analysis):
        """生成超高频用户详细报告"""
        print("正在生成超高频用户报告...")

        report = []
        report.append("# 超高频交易用户深度分析报告")
        report.append("=" * 60)
        report.append("")

        # 基础统计
        report.append("## 1. 超高频用户基础统计")
        report.append(f"- 超高频用户数量（10次以上每天20+小时交易）: {len(super_users)}")
        report.append(f"- 占总用户比例: {len(super_users) / self.df['member_id'].nunique() * 100:.4f}%")
        report.append("")

        # 币种分析
        coin_stats = coin_analysis['overall_stats']
        report.append("## 2. 交易币种分析")
        report.append(f"- 交易币种总数: {len(coin_stats)}")
        report.append("")
        report.append("### 2.1 热门交易币种 TOP 10（按交易次数）")
        for i, row in coin_stats.head(10).iterrows():
            report.append(f"- {row['contract_name']}: {row['transaction_count']:,}次 ({row['transaction_ratio']:.2f}%), "
                         f"交易量: ${row['total_volume_usdt']:,.2f} ({row['volume_ratio']:.2f}%), "
                         f"盈利: ${row['total_profit']:,.2f}")
        report.append("")

        # 盈利分析
        profit_stats = profit_analysis['user_stats']
        report.append("## 3. 盈利能力分析")
        report.append(f"- 总盈利: ${profit_analysis['total_profit']:,.2f}")
        report.append(f"- 总交易量: ${profit_analysis['total_volume']:,.2f}")
        report.append(f"- 整体盈利率: {profit_analysis['overall_profit_rate']:.4f}%")
        report.append(f"- 盈利用户数: {profit_analysis['profitable_users']}")
        report.append(f"- 亏损用户数: {profit_analysis['loss_users']}")
        report.append("")

        report.append("### 3.1 盈利用户 TOP 10")
        for i, row in profit_stats.head(10).iterrows():
            report.append(f"- 用户 {row['member_id'][:8]}***: 总盈利 ${row['total_profit']:,.2f}, "
                         f"交易次数 {row['trade_count']:,}, 盈利率 {row['profit_rate']:.4f}%")
        report.append("")

        report.append("### 3.2 亏损用户 TOP 5")
        loss_users = profit_stats[profit_stats['total_profit'] < 0].tail(5)
        for i, row in loss_users.iterrows():
            report.append(f"- 用户 {row['member_id'][:8]}***: 总亏损 ${row['total_profit']:,.2f}, "
                         f"交易次数 {row['trade_count']:,}, 亏损率 {abs(row['profit_rate']):.4f}%")
        report.append("")

        # 保存报告
        with open('超高频用户分析报告.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))

        # 导出详细数据到Excel
        with pd.ExcelWriter('超高频用户详细数据.xlsx', engine='openpyxl') as writer:
            # 币种统计
            coin_stats.to_excel(writer, sheet_name='币种分析', index=False)

            # 用户盈利统计
            profit_stats.to_excel(writer, sheet_name='用户盈利分析', index=False)

            # 超高频用户列表
            super_user_df = pd.DataFrame({'member_id': super_users})
            super_user_df.to_excel(writer, sheet_name='超高频用户列表', index=False)

        print("超高频用户报告已保存到: 超高频用户分析报告.txt")
        print("超高频用户详细数据已导出到: 超高频用户详细数据.xlsx")

def main():
    """主函数"""
    csv_file = "data/近3个月高频交易分析.csv"

    analyzer = HighFrequencyTradingAnalyzer(csv_file)
    analyzer.generate_analysis_report()
    analyzer.export_detailed_data()

    # 分析超高频用户
    analyzer.analyze_super_high_frequency_users()

if __name__ == "__main__":
    main()
