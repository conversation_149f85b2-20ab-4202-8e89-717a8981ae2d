# -*- coding: utf-8 -*-
"""
指标计算模块
负责各种业务指标的计算逻辑
"""

import pandas as pd
import numpy as np
from config import *

class IndicatorCalculator:
    def __init__(self, data_dict):
        self.register_df = data_dict['register']
        self.problem_df = data_dict['problem'] 
        self.welfare_df = data_dict['welfare']
        
    def identify_problem_users(self):
        """
        识别问题用户
        """
        problem_conditions = []
        
        # 条件1: IP数量超过阈值
        condition1 = self.problem_df['ip_cnt'] > PROBLEM_USER_CONDITIONS['ip_cnt_threshold']
        
        # 条件2: 设备ID数量超过阈值
        condition2 = self.problem_df['device_id_cnt'] > PROBLEM_USER_CONDITIONS['device_id_cnt_threshold']
        
        # 条件3: 邮箱相似 - 检查YES值（不区分大小写）
        condition3 = self.problem_df['similar_email'].str.upper() == PROBLEM_USER_CONDITIONS['similar_email_flag'].upper()
        
        # 条件4: 黑灰名单 - 检查是否不为空且不为NaN
        condition4 = (self.problem_df['black_grey_flag'].notna()) & (self.problem_df['black_grey_flag'] != '') & (self.problem_df['black_grey_flag'] != 'nan')
        
        # 条件5: 风险标签非空
        condition5 = (self.problem_df['risk_label'].notna()) & (self.problem_df['risk_label'] != '') & (self.problem_df['risk_label'] != 'nan')

        # 条件6: 用户风险标识为真正的风险类型
        risk_keywords = ['批量用户', '疑似僵尸用户', '设备聚集', '黑名单', '灰名单', '风险用户', '异常用户']
        condition6 = self.problem_df['user_risk_flag'].str.contains('|'.join(risk_keywords), case=False, na=False)
        
        # 满足任一条件即为问题用户
        problem_mask = condition1 | condition2 | condition3 | condition4 | condition5 | condition6
        
        # 添加问题用户标识
        self.problem_df['is_problem_user'] = problem_mask
        
        # 添加具体问题类型标识
        self.problem_df['is_same_ip'] = condition1
        self.problem_df['is_same_device'] = condition2
        self.problem_df['is_similar_email'] = condition3
        self.problem_df['is_blackgrey'] = condition4
        self.problem_df['is_risk_label'] = condition5
        self.problem_df['is_user_risk'] = condition6
        
        return self.problem_df[problem_mask]
    
    def calculate_daily_country_indicators(self):
        """
        计算按日期和国家分组的各项指标
        """
        results = []

        # 为福利用户表添加国家信息
        welfare_with_country = self.welfare_df.merge(
            self.register_df[['member_id', 'member_country']],
            on='member_id',
            how='left'
        )

        # 获取所有日期和国家的组合
        register_groups = self.register_df.groupby(['register_date', 'member_country'])
        # 问题用户表使用dt_date字段，过滤掉空值
        problem_df_filtered = self.problem_df[self.problem_df['dt_date'].notna()]
        problem_groups = problem_df_filtered.groupby(['dt_date', 'member_country'])
        welfare_groups = welfare_with_country.groupby(['welfare_date', 'member_country'])

        # 获取所有可能的日期和国家组合
        all_dates = set()
        all_countries = set()

        for (date, country), _ in register_groups:
            all_dates.add(date)
            all_countries.add(country)
        for (date, country), _ in problem_groups:
            all_dates.add(date)
            all_countries.add(country)
        for (date, country), _ in welfare_groups:
            all_dates.add(date)
            all_countries.add(country)

        # 为每个日期-国家组合计算指标
        for date in sorted(all_dates):
            for country in sorted(all_countries):
                indicators = self._calculate_single_day_country_indicators(date, country, welfare_with_country)
                if indicators:
                    results.append(indicators)

        return pd.DataFrame(results)
    
    def _calculate_single_day_country_indicators(self, date, country, welfare_with_country):
        """
        计算单个日期-国家组合的指标
        """
        # 获取当日当国家的数据
        register_data = self.register_df[
            (self.register_df['register_date'] == date) &
            (self.register_df['member_country'] == country)
        ]

        # 问题用户数据：当天注册的问题用户  
        problem_data = self.problem_df[
            (self.problem_df['register_date'] == date) &
            (self.problem_df['member_country'] == country)
        ]

        welfare_data = welfare_with_country[
            (welfare_with_country['welfare_date'] == date) &
            (welfare_with_country['member_country'] == country)
        ]
        
        # 如果注册数据和福利数据都为空，跳过
        if len(register_data) == 0 and len(welfare_data) == 0:
            return None
            
        # 基础注册指标
        register_count = len(register_data)
        kyc_count = len(register_data[register_data['kyc_level'] == KYC_LEVELS['completed']]) if register_count > 0 else 0
        
        # 活动参与指标
        welfare_count = len(welfare_data)
        welfare_ratio = (welfare_count / register_count * 100) if register_count > 0 else 0
        total_bonus = welfare_data['total_bonus'].sum() if len(welfare_data) > 0 else 0
        
        # 问题用户指标
        problem_count = len(problem_data[problem_data['is_problem_user'] == True]) if len(problem_data) > 0 else 0
        problem_ratio = (problem_count / register_count * 100) if register_count > 0 else 0
        
        # 问题用户奖励计算
        problem_user_ids = problem_data[problem_data['is_problem_user'] == True]['member_id'].tolist()
        problem_welfare_data = welfare_data[welfare_data['member_id'].isin(problem_user_ids)]
        problem_bonus_total = problem_welfare_data['total_bonus'].sum()
        problem_welfare_count = len(problem_welfare_data)
        
        # 新增：问题用户福利金详细统计
        problem_newuser_bonus = problem_welfare_data['newuser_bonus'].sum() if len(problem_welfare_data) > 0 else 0
        problem_position_bonus = problem_welfare_data['POSITION_bonus'].sum() if len(problem_welfare_data) > 0 else 0
        total_problem_users = len(problem_user_ids)  # 当天总问题用户数
        problem_users_got_welfare = len(problem_welfare_data)  # 实际领取福利的问题用户数
        problem_welfare_rate = (problem_users_got_welfare / total_problem_users * 100) if total_problem_users > 0 else 0
        
        # 细分问题类型统计
        same_ip_count = len(problem_data[problem_data['is_same_ip'] == True]) if len(problem_data) > 0 else 0
        same_device_count = len(problem_data[problem_data['is_same_device'] == True]) if len(problem_data) > 0 else 0
        similar_email_count = len(problem_data[problem_data['is_similar_email'] == True]) if len(problem_data) > 0 else 0
        
        # 问题用户中KYC用户统计
        kyc_problem_device_count = 0
        kyc_problem_email_count = 0
        
        if len(problem_data) > 0:
            # 需要关联注册表获取KYC信息
            problem_with_kyc = problem_data.merge(
                self.register_df[['member_id', 'kyc_level']],
                on='member_id',
                how='left'
            )

            # 同设备ID问题用户中已完成KYC的数量
            kyc_problem_device_count = len(problem_with_kyc[
                (problem_with_kyc['is_same_device'] == True) &
                (problem_with_kyc['kyc_level'] == KYC_LEVELS['completed'])
            ])

            # 邮箱相似问题用户中已完成KYC的数量
            kyc_problem_email_count = len(problem_with_kyc[
                (problem_with_kyc['is_similar_email'] == True) &
                (problem_with_kyc['kyc_level'] == KYC_LEVELS['completed'])
            ])
        
        return {
            '日期': date,
            '国家': country,
            '注册用户数': register_count,
            'KYC用户数': kyc_count,
            '参与活动用户数': welfare_count,
            '新用户活跃比例(%)': round(welfare_ratio, 2),
            '活动奖励总额': round(total_bonus, 2),
            '问题用户数': problem_count,
            '风控警报密度(%)': round(problem_ratio, 2),
            '问题用户奖励总额': round(problem_bonus_total, 2),
            '问题用户参与活动数': problem_welfare_count,
            '问题用户参与活动奖励': round(problem_bonus_total, 2),
            '问题用户领取福利人数': problem_users_got_welfare,
            '问题用户福利参与率(%)': round(problem_welfare_rate, 2),
            '问题用户新手奖励': round(problem_newuser_bonus, 2),
            '问题用户仓位奖励': round(problem_position_bonus, 2),
            '同IP用户数': same_ip_count,
            '同设备ID用户数': same_device_count,
            '邮箱相似用户数': similar_email_count,
            '同设备ID用户中KYC数量': kyc_problem_device_count,
            '邮箱相似用户中KYC数量': kyc_problem_email_count
        }
    
    def get_summary_statistics(self):
        """
        获取汇总统计信息
        """
        # 识别问题用户
        problem_users = self.identify_problem_users()
        
        summary = {
            '总注册用户数': len(self.register_df),
            '总KYC用户数': len(self.register_df[self.register_df['kyc_level'] == KYC_LEVELS['completed']]),
            '总参与活动用户数': len(self.welfare_df),
            '总问题用户数': len(problem_users),
            '总活动奖励': self.welfare_df['total_bonus'].sum(),
            '问题用户获得奖励': self.welfare_df[
                self.welfare_df['member_id'].isin(problem_users['member_id'])
            ]['total_bonus'].sum(),
            '同IP用户数': len(problem_users[problem_users['is_same_ip'] == True]),
            '同设备ID用户数': len(problem_users[problem_users['is_same_device'] == True]),
            '邮箱相似用户数': len(problem_users[problem_users['is_similar_email'] == True]),
        }
        
        return summary

    def calculate_most_freq_via_stats(self):
        """
        计算问题用户的most_freq_via统计
        """
        try:
            # 读取注册总表获取most_freq_via信息
            import pandas as pd
            df_register_full = pd.read_excel('data/真实数据表格.xlsx', sheet_name='总注册用户表格')

            # 获取问题用户（只获取被识别为问题用户的记录）
            problem_users = self.identify_problem_users()
            print(f"识别出的问题用户数量: {len(problem_users)}")

            # 合并数据，获取问题用户的most_freq_via信息
            merged_df = problem_users.merge(
                df_register_full[['member_id', 'most_freq_via']],
                on='member_id',
                how='left'
            )

            print(f"合并后的问题用户数量: {len(merged_df)}")

            # 过滤掉空白值
            valid_data = merged_df[
                merged_df['most_freq_via'].notna() &
                (merged_df['most_freq_via'] != '') &
                (merged_df['most_freq_via'] != 'nan')
            ]

            if len(valid_data) == 0:
                return {
                    'total_valid_users': 0,
                    'app_users': 0,
                    'web_users': 0,
                    'app_percentage': 0.0,
                    'web_percentage': 0.0,
                    'detailed_stats': {},
                    'raw_data': merged_df
                }

            # 分类统计
            # app包含ios和Android，web包含WEB和H5
            app_types = ['ios', 'Android', 'IOS', 'ANDROID', 'iOS']
            web_types = ['WEB', 'H5', 'web', 'h5']

            # 统计app和web
            app_count = valid_data[valid_data['most_freq_via'].isin(app_types)].shape[0]
            web_count = valid_data[valid_data['most_freq_via'].isin(web_types)].shape[0]
            other_count = len(valid_data) - app_count - web_count

            total_valid = len(valid_data)

            # 详细分类统计
            detailed_stats = {}
            for via_type in valid_data['most_freq_via'].unique():
                count = (valid_data['most_freq_via'] == via_type).sum()
                percentage = count / total_valid * 100
                category = "APP" if via_type in app_types else ("WEB" if via_type in web_types else "其他")
                detailed_stats[via_type] = {
                    'count': count,
                    'percentage': percentage,
                    'category': category
                }

            return {
                'total_valid_users': total_valid,
                'app_users': app_count,
                'web_users': web_count,
                'other_users': other_count,
                'app_percentage': round(app_count / total_valid * 100, 2),
                'web_percentage': round(web_count / total_valid * 100, 2),
                'other_percentage': round(other_count / total_valid * 100, 2),
                'detailed_stats': detailed_stats,
                'raw_data': merged_df
            }

        except Exception as e:
            print(f"计算most_freq_via统计时出错: {e}")
            return {
                'total_valid_users': 0,
                'app_users': 0,
                'web_users': 0,
                'app_percentage': 0.0,
                'web_percentage': 0.0,
                'detailed_stats': {},
                'error': str(e)
            }
