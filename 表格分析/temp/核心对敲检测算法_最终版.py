#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心对敲检测算法 - 最终版
"""

def calculate_hedge_score(profit_a, profit_b, expected_scale=None):
    """
    核心对敲检测算法
    
    Args:
        profit_a: 仓位A的实际盈亏
        profit_b: 仓位B的实际盈亏
        expected_scale: 预期交易规模（可选）
    
    Returns:
        float: 对敲评分 (0-1，越高风险越大)
    """
    
    # 基础检查
    profit_sum = abs(profit_a) + abs(profit_b)
    if profit_sum == 0:
        return 0.0
    
    # 判断是否同边
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        # 异边（一盈一亏）：使用原始公式
        total_profit = profit_a + profit_b
        return 1.0 - abs(total_profit) / profit_sum
    
    else:
        # 同边（双盈/双亏）：使用接近0(30%) + 相对波动率(70%)
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        # 推测交易规模
        if expected_scale and expected_scale > 0:
            scale = expected_scale
        else:
            if max_abs <= 1:
                scale = 10.0
            elif max_abs <= 10:
                scale = 100.0
            elif max_abs <= 100:
                scale = 1000.0
            else:
                scale = max_abs * 10
        
        # 因子1: 接近0程度评分（30%权重）
        if min_abs <= 0.1:
            closeness_score = 0.9
        elif min_abs <= 1:
            closeness_score = 0.8
        elif min_abs <= 5:
            closeness_score = 0.6
        elif min_abs <= 10:
            closeness_score = 0.4
        else:
            closeness_score = 0.1 if max_abs >= 20 else 0.2
        
        # 因子2: 相对波动率评分（70%权重）
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:
            volatility_score = 0.9
        elif max_relative <= 0.05:
            volatility_score = 0.8
        elif max_relative <= 0.1:
            volatility_score = 0.6
        elif max_relative <= 0.2:
            volatility_score = 0.4
        else:
            volatility_score = 0.2
        
        # 综合评分
        return 0.3 * closeness_score + 0.7 * volatility_score


# 测试用例
if __name__ == "__main__":
    test_cases = [
        # 异边情况
        (10.0, -9.5, 100.0, "异边-几乎抵消"),
        (1.0, -0.9, 100.0, "异边-小金额"),
        
        # 同边情况 - 你的例子
        (-0.03, -1.0, 10.0, "同边双亏-10U规模"),
        (-0.03, -1.0, 100.0, "同边双亏-100U规模"),
        
        # 同边情况 - 一方接近0
        (-0.1, -5.0, 100.0, "同边双亏-一方很接近0"),
        (-1.0, -10.0, 100.0, "同边双亏-一方接近0"),
        
        # 同边情况 - 都远离0
        (-20.0, -20.0, 100.0, "同边双亏-都远离0"),
        (-50.0, -60.0, 100.0, "同边双亏-都很远离0"),
        
        # 双盈情况
        (0.1, 5.0, 100.0, "同边双盈-一方接近0"),
        (20.0, 25.0, 100.0, "同边双盈-都远离0"),
    ]
    
    print("=== 核心对敲检测算法测试 ===\n")
    
    for i, (a, b, scale, desc) in enumerate(test_cases, 1):
        score = calculate_hedge_score(a, b, scale)
        
        if score >= 0.8:
            risk = "高风险对敲"
        elif score >= 0.6:
            risk = "中等风险对敲"
        elif score >= 0.4:
            risk = "低风险对敲"
        else:
            risk = "正常交易"
        
        print(f"{i:2d}. {desc}")
        print(f"    仓位: A={a}, B={b} (规模{scale}U)")
        print(f"    评分: {score:.3f} ({risk})")
        print()
