#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后对敲检测测试 - 测试调整权重和阈值后的效果
"""

import random
import math

def calculate_hedge_score_optimized(profit_a, profit_b, expected_scale=None):
    """
    优化后的核心对敲检测算法
    主要优化：
    1. 权重调整：接近0(60%) + 相对波动率(40%)
    2. 阈值放宽：让更多情况能得到合理评分
    """
    
    # 基础检查
    profit_sum = abs(profit_a) + abs(profit_b)
    if profit_sum == 0:
        return 0.0
    
    # 判断是否同边
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        # 异边（一盈一亏）：使用原始公式
        total_profit = profit_a + profit_b
        return 1.0 - abs(total_profit) / profit_sum
    
    else:
        # 同边（双盈/双亏）：优化后的接近0 + 相对波动率
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        # 改进的规模推测逻辑
        if expected_scale and expected_scale > 0:
            scale = expected_scale
        else:
            total_abs = abs_a + abs_b
            if total_abs <= 2:
                scale = 10.0
            elif total_abs <= 20:
                scale = 100.0
            elif total_abs <= 200:
                scale = 1000.0
            elif total_abs <= 2000:
                scale = 10000.0
            else:
                scale = total_abs * 5  # 更保守的倍数
        
        # 优化的接近0程度评分（放宽阈值）
        if min_abs <= 0.2:  # 从0.1放宽到0.2
            closeness_score = 0.9
        elif min_abs <= 2:   # 从1放宽到2
            closeness_score = 0.8
        elif min_abs <= 8:   # 从5放宽到8
            closeness_score = 0.6
        elif min_abs <= 15:  # 从10放宽到15
            closeness_score = 0.4
        elif min_abs <= 30:  # 从20放宽到30
            closeness_score = 0.2
        else:
            closeness_score = 0.1
        
        # 相对波动率评分（保持不变）
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:
            volatility_score = 0.9
        elif max_relative <= 0.05:
            volatility_score = 0.8
        elif max_relative <= 0.1:
            volatility_score = 0.6
        elif max_relative <= 0.2:
            volatility_score = 0.4
        else:
            volatility_score = 0.2
        
        # 优化的权重：接近0(60%) + 相对波动率(40%)
        return 0.6 * closeness_score + 0.4 * volatility_score


def calculate_hedge_score_original(profit_a, profit_b, expected_scale=None):
    """原始算法（用于对比）"""
    
    profit_sum = abs(profit_a) + abs(profit_b)
    if profit_sum == 0:
        return 0.0
    
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        total_profit = profit_a + profit_b
        return 1.0 - abs(total_profit) / profit_sum
    else:
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        max_abs = max(abs_a, abs_b)
        
        if expected_scale and expected_scale > 0:
            scale = expected_scale
        else:
            if max_abs <= 1:
                scale = 10.0
            elif max_abs <= 10:
                scale = 100.0
            elif max_abs <= 100:
                scale = 1000.0
            else:
                scale = max_abs * 10
        
        # 原始阈值
        if min_abs <= 0.1:
            closeness_score = 0.9
        elif min_abs <= 1:
            closeness_score = 0.8
        elif min_abs <= 5:
            closeness_score = 0.6
        elif min_abs <= 10:
            closeness_score = 0.4
        else:
            closeness_score = 0.1 if max_abs >= 20 else 0.2
        
        max_relative = max(abs_a / scale, abs_b / scale)
        if max_relative <= 0.01:
            volatility_score = 0.9
        elif max_relative <= 0.05:
            volatility_score = 0.8
        elif max_relative <= 0.1:
            volatility_score = 0.6
        elif max_relative <= 0.2:
            volatility_score = 0.4
        else:
            volatility_score = 0.2
        
        # 原始权重：接近0(40%) + 相对波动率(60%)
        return 0.4 * closeness_score + 0.6 * volatility_score


def generate_test_cases():
    """生成1000个测试用例"""
    
    test_cases = []
    scales = [10, 100, 1000, 10000, 100000, 500000]
    case_id = 1
    
    for scale in scales:
        # 每个规模生成不同类型的测试用例
        
        # 异边情况（一盈一亏）
        for _ in range(15):
            # 高风险对敲：接近抵消
            profit_a = random.uniform(0.1 * scale, 0.3 * scale)
            profit_b = -random.uniform(0.8 * profit_a, 1.2 * profit_a)
            test_cases.append((case_id, profit_a, profit_b, scale, "异边-高风险"))
            case_id += 1
            
            # 中等风险对敲：部分抵消
            profit_a = random.uniform(0.1 * scale, 0.2 * scale)
            profit_b = -random.uniform(0.5 * profit_a, 0.8 * profit_a)
            test_cases.append((case_id, profit_a, profit_b, scale, "异边-中等风险"))
            case_id += 1
        
        # 同边双亏情况
        for _ in range(20):
            # 高风险对敲：一方很接近0
            close_to_zero = random.uniform(0.001 * scale, 0.02 * scale)
            far_from_zero = random.uniform(0.05 * scale, 0.2 * scale)
            test_cases.append((case_id, -close_to_zero, -far_from_zero, scale, "同边双亏-高风险"))
            case_id += 1
            
            # 中等风险对敲：一方接近0
            close_to_zero = random.uniform(0.02 * scale, 0.08 * scale)
            far_from_zero = random.uniform(0.1 * scale, 0.3 * scale)
            test_cases.append((case_id, -close_to_zero, -far_from_zero, scale, "同边双亏-中等风险"))
            case_id += 1
            
            # 正常交易：都远离0
            loss_a = random.uniform(0.2 * scale, 0.5 * scale)
            loss_b = random.uniform(0.2 * scale, 0.5 * scale)
            test_cases.append((case_id, -loss_a, -loss_b, scale, "同边双亏-正常交易"))
            case_id += 1
        
        # 同边双盈情况
        for _ in range(20):
            # 高风险对敲：一方很接近0
            close_to_zero = random.uniform(0.001 * scale, 0.02 * scale)
            far_from_zero = random.uniform(0.05 * scale, 0.2 * scale)
            test_cases.append((case_id, close_to_zero, far_from_zero, scale, "同边双盈-高风险"))
            case_id += 1
            
            # 中等风险对敲：一方接近0
            close_to_zero = random.uniform(0.02 * scale, 0.08 * scale)
            far_from_zero = random.uniform(0.1 * scale, 0.3 * scale)
            test_cases.append((case_id, close_to_zero, far_from_zero, scale, "同边双盈-中等风险"))
            case_id += 1
            
            # 正常交易：都远离0
            profit_a = random.uniform(0.2 * scale, 0.5 * scale)
            profit_b = random.uniform(0.2 * scale, 0.5 * scale)
            test_cases.append((case_id, profit_a, profit_b, scale, "同边双盈-正常交易"))
            case_id += 1
    
    return test_cases


def compare_algorithms():
    """对比原始算法和优化算法"""
    
    print("=== 优化后对敲检测算法对比测试 ===")
    print("生成1000+个测试用例，对比原始算法和优化算法...")
    
    test_cases = generate_test_cases()
    print(f"实际生成 {len(test_cases)} 个测试用例\n")
    
    original_results = []
    optimized_results = []
    
    for case_id, profit_a, profit_b, scale, expected_type in test_cases:
        # 原始算法
        original_score = calculate_hedge_score_original(profit_a, profit_b, scale)
        if original_score >= 0.8:
            original_risk = "高风险对敲"
        elif original_score >= 0.6:
            original_risk = "中等风险对敲"
        elif original_score >= 0.4:
            original_risk = "低风险对敲"
        else:
            original_risk = "正常交易"
        
        # 优化算法
        optimized_score = calculate_hedge_score_optimized(profit_a, profit_b, scale)
        if optimized_score >= 0.8:
            optimized_risk = "高风险对敲"
        elif optimized_score >= 0.6:
            optimized_risk = "中等风险对敲"
        elif optimized_score >= 0.4:
            optimized_risk = "低风险对敲"
        else:
            optimized_risk = "正常交易"
        
        original_results.append((case_id, profit_a, profit_b, scale, expected_type, original_score, original_risk))
        optimized_results.append((case_id, profit_a, profit_b, scale, expected_type, optimized_score, optimized_risk))
    
    # 分析对比结果
    analyze_comparison(original_results, optimized_results)


def analyze_comparison(original_results, optimized_results):
    """分析对比结果"""
    
    print("=== 算法对比分析 ===\n")
    
    # 按预期类型分组统计
    type_stats = {}
    
    for i, (case_id, profit_a, profit_b, scale, expected_type, _, _) in enumerate(original_results):
        if expected_type not in type_stats:
            type_stats[expected_type] = {
                'count': 0,
                'original_scores': [],
                'optimized_scores': [],
                'original_avg': 0,
                'optimized_avg': 0,
                'improvement': 0
            }
        
        type_stats[expected_type]['count'] += 1
        type_stats[expected_type]['original_scores'].append(original_results[i][5])
        type_stats[expected_type]['optimized_scores'].append(optimized_results[i][5])
    
    # 计算平均分和改进幅度
    for type_name in type_stats:
        stats = type_stats[type_name]
        stats['original_avg'] = sum(stats['original_scores']) / len(stats['original_scores'])
        stats['optimized_avg'] = sum(stats['optimized_scores']) / len(stats['optimized_scores'])
        stats['improvement'] = stats['optimized_avg'] - stats['original_avg']
    
    # 输出对比结果
    print("按预期类型对比：")
    print(f"{'类型':<20} {'案例数':<8} {'原始平均':<10} {'优化平均':<10} {'改进幅度':<10}")
    print("-" * 70)
    
    for type_name in sorted(type_stats.keys()):
        stats = type_stats[type_name]
        improvement_str = f"{stats['improvement']:+.3f}"
        print(f"{type_name:<20} {stats['count']:<8} {stats['original_avg']:<10.3f} {stats['optimized_avg']:<10.3f} {improvement_str:<10}")
    
    # 重点关注同边高风险对敲的改进
    print("\n=== 重点关注：同边高风险对敲改进效果 ===")
    
    same_side_high_risk = [k for k in type_stats.keys() if "高风险" in k and "同边" in k]
    for type_name in same_side_high_risk:
        stats = type_stats[type_name]
        print(f"\n{type_name}:")
        print(f"  原始算法平均评分: {stats['original_avg']:.3f}")
        print(f"  优化算法平均评分: {stats['optimized_avg']:.3f}")
        print(f"  改进幅度: {stats['improvement']:+.3f}")
        
        # 统计评分分布
        original_high = sum(1 for score in stats['original_scores'] if score >= 0.8)
        optimized_high = sum(1 for score in stats['optimized_scores'] if score >= 0.8)
        
        print(f"  原始算法高风险识别率: {original_high}/{stats['count']} ({original_high/stats['count']*100:.1f}%)")
        print(f"  优化算法高风险识别率: {optimized_high}/{stats['count']} ({optimized_high/stats['count']*100:.1f}%)")


if __name__ == "__main__":
    compare_algorithms()
