# -*- coding: utf-8 -*-
"""
报告生成模块
负责生成分析报告和输出结果
"""

import pandas as pd
import os
from datetime import datetime
from config import *

class ReportGenerator:
    def __init__(self):
        self.results_df = None
        self.summary_stats = None
        
    def set_results(self, results_df, summary_stats):
        """
        设置分析结果数据
        """
        self.results_df = results_df
        self.summary_stats = summary_stats
        
    def generate_csv_report(self, output_path=None):
        """
        生成CSV格式报告
        """
        if self.results_df is None:
            print("错误: 没有分析结果数据")
            return False
            
        try:
            if not output_path:
                # 创建输出目录
                os.makedirs(OUTPUT_DIR, exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(OUTPUT_DIR, f"analysis_result_{timestamp}.csv")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存结果
            self.results_df.to_csv(output_path, index=False, encoding='utf-8-sig')
            print(f"CSV报告已保存到: {output_path}")
            
            return output_path
            
        except Exception as e:
            print(f"生成CSV报告失败: {str(e)}")
            return False
    
    def generate_summary_report(self, output_path=None):
        """
        生成汇总报告
        """
        if self.summary_stats is None:
            print("错误: 没有汇总统计数据")
            return False
            
        try:
            if not output_path:
                os.makedirs(OUTPUT_DIR, exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(OUTPUT_DIR, f"summary_report_{timestamp}.txt")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("=" * 50 + "\n")
                f.write("用户数据分析汇总报告\n")
                f.write("=" * 50 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write("整体统计:\n")
                f.write("-" * 30 + "\n")
                for key, value in self.summary_stats.items():
                    if isinstance(value, float):
                        f.write(f"{key}: {value:,.2f}\n")
                    else:
                        f.write(f"{key}: {value:,}\n")
                
                f.write("\n按日期国家详细统计:\n")
                f.write("-" * 30 + "\n")
                f.write(f"共 {len(self.results_df)} 条记录\n")
                
                # 按国家汇总
                if len(self.results_df) > 0:
                    country_summary = self.results_df.groupby('国家').agg({
                        '注册用户数': 'sum',
                        'KYC用户数': 'sum',
                        '参与活动用户数': 'sum',
                        '问题用户数': 'sum',
                        '活动奖励总额': 'sum'
                    })
                    
                    f.write("\n按国家汇总:\n")
                    for country, row in country_summary.iterrows():
                        f.write(f"\n{country}:\n")
                        f.write(f"  注册用户: {row['注册用户数']:,}\n")
                        f.write(f"  KYC用户: {row['KYC用户数']:,}\n")
                        f.write(f"  参与活动: {row['参与活动用户数']:,}\n")
                        f.write(f"  问题用户: {row['问题用户数']:,}\n")
                        f.write(f"  奖励总额: {row['活动奖励总额']:,.2f}\n")
            
            print(f"汇总报告已保存到: {output_path}")
            return output_path
            
        except Exception as e:
            print(f"生成汇总报告失败: {str(e)}")
            return False
    
    def print_console_report(self):
        """
        在控制台打印报告
        """
        if self.results_df is None or self.summary_stats is None:
            print("错误: 没有分析结果数据")
            return
            
        print("\n" + "=" * 60)
        print("用户数据分析结果")
        print("=" * 60)
        
        # 打印汇总统计
        print("\n整体统计:")
        print("-" * 40)
        for key, value in self.summary_stats.items():
            if isinstance(value, float):
                print(f"{key}: {value:,.2f}")
            else:
                print(f"{key}: {value:,}")
        
        # 打印前10条详细记录
        print(f"\n详细统计 (前10条记录):")
        print("-" * 40)
        if len(self.results_df) > 0:
            # 选择关键字段显示
            display_columns = [
                '日期', '国家', '注册用户数', 'KYC用户数', 
                '参与活动用户数', '问题用户数', '活动奖励总额'
            ]
            display_df = self.results_df[display_columns].head(10)
            print(display_df.to_string(index=False))
            
            if len(self.results_df) > 10:
                print(f"\n... 还有 {len(self.results_df) - 10} 条记录")
        else:
            print("没有数据")

    def print_most_freq_via_report(self, calculator):
        """
        打印most_freq_via统计报告
        """
        print("\n" + "=" * 60)
        print("问题用户访问渠道统计 (most_freq_via)")
        print("=" * 60)

        try:
            via_stats = calculator.calculate_most_freq_via_stats()

            if 'error' in via_stats:
                print(f"统计计算出错: {via_stats['error']}")
                return

            if via_stats['total_valid_users'] == 0:
                print("没有有效的访问渠道数据")
                return

            # 打印总体统计
            print(f"\n总体统计:")
            print("-" * 40)
            print(f"总有效用户数: {via_stats['total_valid_users']:,}")
            print(f"APP用户数: {via_stats['app_users']:,} ({via_stats['app_percentage']:.2f}%)")
            print(f"WEB用户数: {via_stats['web_users']:,} ({via_stats['web_percentage']:.2f}%)")
            if via_stats.get('other_users', 0) > 0:
                print(f"其他类型: {via_stats['other_users']:,} ({via_stats['other_percentage']:.2f}%)")

            # 打印详细分类统计
            print(f"\n详细分类统计:")
            print("-" * 40)
            detailed_stats = via_stats['detailed_stats']

            # 按数量排序
            sorted_stats = sorted(detailed_stats.items(), key=lambda x: x[1]['count'], reverse=True)

            for via_type, stats in sorted_stats:
                category_icon = "📱" if stats['category'] == "APP" else ("🌐" if stats['category'] == "WEB" else "❓")
                print(f"{category_icon} {via_type}: {stats['count']:,}个 ({stats['percentage']:.2f}%) - {stats['category']}")

            # 保存详细结果到文件
            self._save_via_stats_to_file(via_stats)

        except Exception as e:
            print(f"生成most_freq_via报告时出错: {e}")

    def _save_via_stats_to_file(self, via_stats):
        """
        保存most_freq_via统计结果到文件
        """
        try:
            os.makedirs(OUTPUT_DIR, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = os.path.join(OUTPUT_DIR, f"most_freq_via_stats_{timestamp}.txt")

            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("问题用户访问渠道统计 (most_freq_via)\n")
                f.write("=" * 60 + "\n\n")

                f.write("总体统计:\n")
                f.write("-" * 40 + "\n")
                f.write(f"总有效用户数: {via_stats['total_valid_users']:,}\n")
                f.write(f"APP用户数: {via_stats['app_users']:,} ({via_stats['app_percentage']:.2f}%)\n")
                f.write(f"WEB用户数: {via_stats['web_users']:,} ({via_stats['web_percentage']:.2f}%)\n")
                if via_stats.get('other_users', 0) > 0:
                    f.write(f"其他类型: {via_stats['other_users']:,} ({via_stats['other_percentage']:.2f}%)\n")

                f.write(f"\n详细分类统计:\n")
                f.write("-" * 40 + "\n")

                detailed_stats = via_stats['detailed_stats']
                sorted_stats = sorted(detailed_stats.items(), key=lambda x: x[1]['count'], reverse=True)

                for via_type, stats in sorted_stats:
                    f.write(f"{via_type}: {stats['count']:,}个 ({stats['percentage']:.2f}%) - {stats['category']}\n")

            print(f"most_freq_via统计结果已保存到: {output_path}")

        except Exception as e:
            print(f"保存most_freq_via统计结果失败: {e}")
    
    def validate_results(self):
        """
        验证结果数据
        """
        issues = []
        
        if self.results_df is None:
            issues.append("缺少分析结果数据")
        elif len(self.results_df) == 0:
            issues.append("分析结果为空")
        else:
            # 检查必要字段
            required_columns = OUTPUT_COLUMNS
            missing_columns = [col for col in required_columns if col not in self.results_df.columns]
            if missing_columns:
                issues.append(f"结果数据缺少字段: {missing_columns}")
            
            # 检查数据类型
            numeric_columns = ['注册用户数', 'KYC用户数', '参与活动用户数', '问题用户数']
            for col in numeric_columns:
                if col in self.results_df.columns:
                    if not pd.api.types.is_numeric_dtype(self.results_df[col]):
                        issues.append(f"字段 {col} 应为数值类型")
        
        if self.summary_stats is None:
            issues.append("缺少汇总统计数据")
        
        if issues:
            print("结果验证发现问题:")
            for issue in issues:
                print(f"- {issue}")
            return False
        else:
            print("结果验证通过")
            return True
    
    def export_excel_report(self, output_path=None):
        """
        导出Excel格式报告 (可选功能)
        """
        try:
            import openpyxl
            
            if not output_path:
                os.makedirs(OUTPUT_DIR, exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(OUTPUT_DIR, f"analysis_result_{timestamp}.xlsx")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 详细数据表
                self.results_df.to_excel(writer, sheet_name='详细统计', index=False)
                
                # 汇总数据表
                summary_df = pd.DataFrame(list(self.summary_stats.items()), 
                                        columns=['指标', '数值'])
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
                
                # 按国家汇总表
                if len(self.results_df) > 0:
                    country_summary = self.results_df.groupby('国家').agg({
                        '注册用户数': 'sum',
                        'KYC用户数': 'sum', 
                        '参与活动用户数': 'sum',
                        '问题用户数': 'sum',
                        '活动奖励总额': 'sum'
                    }).reset_index()
                    country_summary.to_excel(writer, sheet_name='按国家汇总', index=False)
            
            print(f"Excel报告已保存到: {output_path}")
            return output_path
            
        except ImportError:
            print("警告: 未安装openpyxl，无法生成Excel报告")
            return False
        except Exception as e:
            print(f"生成Excel报告失败: {str(e)}")
            return False
