# -*- coding: utf-8 -*-
"""
用户数据分析系统配置文件
"""

import os

# 文件路径配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, 'data')
OUTPUT_DIR = os.path.join(BASE_DIR, 'output')

# 数据文件配置
DATA_FILES = {
    'register_users': 'sheet_2.csv',  # 总注册用户表格
    'problem_users': 'problem_users.csv',
    'welfare_users': 'welfare_users.csv'
}

# 日期格式配置
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'  # 真实数据格式
OUTPUT_DATE_FORMAT = '%Y-%m-%d'

# 问题用户判断条件
PROBLEM_USER_CONDITIONS = {
    'ip_cnt_threshold': 10,          # IP数量阈值
    'device_id_cnt_threshold': 2,    # 设备ID数量阈值
    'similar_email_flag': 'yes',     # 邮箱相似标识
    'black_grey_keywords': ['黑名单', '灰名单'],  # 黑灰名单关键词
}

# KYC等级配置 (实际字段名为auth_level)
KYC_LEVELS = {
    'not_kyc': 1,    # 未KYC
    'completed': 3   # 已完成KYC
}

# 输出字段配置
OUTPUT_COLUMNS = [
    '日期',
    '国家',
    '注册用户数',
    'KYC用户数',
    '参与活动用户数',
    '新用户活跃比例(%)',
    '活动奖励总额',
    '问题用户数',
    '风控警报密度(%)',
    '问题用户奖励总额',
    '问题用户参与活动数',
    '问题用户参与活动奖励',
    '问题用户领取福利人数',
    '问题用户福利参与率(%)',
    '问题用户新手奖励',
    '问题用户仓位奖励',
    '同IP用户数',
    '同设备ID用户数',
    '邮箱相似用户数',
    '同设备ID用户中KYC数量',
    '邮箱相似用户中KYC数量'
]

# 默认输出文件名
DEFAULT_OUTPUT_FILE = 'user_analysis_result.csv'
