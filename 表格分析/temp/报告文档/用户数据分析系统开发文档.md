# 用户数据分析系统开发文档

## 1. 项目概述

### 1.1 项目目标
开发一个用户数据分析系统，用于分析平台用户的注册、活动参与、问题用户等多维度数据，为业务决策提供数据支持。

### 1.2 核心功能
- 多表数据关联分析
- 按日期和国家维度统计用户行为
- 问题用户识别和分类统计
- 活动参与度和奖励分析
- KYC用户行为分析
- **问题用户访问渠道统计 (most_freq_via)**

## 2. 数据源设计

### 2.1 数据表结构

#### 表1：总注册用户表 (register_users.csv)
| 字段名 | 数据类型 | 说明 | 示例 |
|--------|----------|------|------|
| member_id | String | 用户唯一标识 | U123456 |
| register_time | String | 注册时间 | 2025/6/19 0:07:23 |
| member_country | String | 用户国家 | CN, US, JP |
| kyc_level | Integer | KYC等级 | 1(未KYC), 3(已KYC) |
| most_freq_via | String | 最常用访问渠道 | Android, iOS, WEB, H5 |

#### 表2：问题用户表 (problem_users.csv)
| 字段名 | 数据类型 | 说明 | 示例 |
|--------|----------|------|------|
| member_id | String | 用户唯一标识 | U123456 |
| agent_flag | String | 代理标识(非问题标签) | Y/N |
| member_country | String | 用户国家 | CN |
| register_time | String | 注册时间 | 2025/6/19 0:07:23 |
| ip_cnt | Integer | IP地址数量 | 6 |
| device_id_cnt | Integer | 设备ID数量 | 3 |
| black_grey_flag | String | 黑灰名单标识 | 黑名单/灰名单 |
| risk_label | String | 风险标签 | 高风险 |
| create_time | String | 问题发现时间 | 2025/6/20 10:30:00 |
| similar_email | String | 邮箱相似标识 | yes/no |
| user_risk_flag | String | 用户风险标识 | 风险用户 |
| dt | String | 数据日期 | 2025-06-20 |

#### 表3：福利用户表 (welfare_users.csv)
| 字段名 | 数据类型 | 说明 | 示例 |
|--------|----------|------|------|
| member_id | String | 用户唯一标识 | U123456 |
| newuser_bonus | Float | 新用户奖励 | 100.0 |
| POSITION_bonus | Float | 持仓奖励 | 50.0 |
| register_time | String | 注册时间 | 2025/6/19 0:07:23 |
| dt | String | 奖励发放日期 | 2025-06-20 |

### 2.2 数据关联逻辑
- **主键关联**：通过 member_id 关联三张表
- **时间维度**：
  - 注册相关指标：使用表1的 register_time
  - 问题用户指标：使用表2的 create_time
  - 福利相关指标：使用表3的 dt
- **国家维度**：以表1的 member_country 为准

## 3. 业务规则定义

### 3.1 问题用户判断条件
用户满足以下**任一条件**即为问题用户：
- `device_id_cnt > 2` (同设备ID用户)
- `ip_cnt > 5` (同IP用户)
- `similar_email = 'yes'` (邮箱相似用户)
- `black_grey_flag` 包含"黑名单"或"灰名单"
- `risk_label` 非空白
- `user_risk_flag` 非空白

### 3.2 KYC用户定义
- kyc_level = 1：未完成KYC
- kyc_level = 3：已完成KYC

### 3.3 奖励计算规则
- 总奖励 = newuser_bonus + POSITION_bonus
- 0值表示未领取奖励

## 4. 指标计算规范

### 4.1 基础指标
| 指标名称 | 计算公式 | 数据源 |
|----------|----------|--------|
| 注册用户数 | COUNT(DISTINCT member_id) | 表1 |
| KYC用户数 | COUNT(member_id WHERE kyc_level = 3) | 表1 |
| 参与活动用户数 | COUNT(DISTINCT member_id) | 表3 |
| 参与活动比例 | 参与活动用户数 / 注册用户数 * 100% | 表1+表3 |
| 活动奖励总额 | SUM(newuser_bonus + POSITION_bonus) | 表3 |

### 4.2 问题用户指标
| 指标名称 | 计算公式 | 数据源 |
|----------|----------|--------|
| 问题用户数 | COUNT(DISTINCT member_id WHERE 问题条件) | 表2 |
| 问题用户比例 | 问题用户数 / 注册用户数 * 100% | 表1+表2 |
| 问题用户奖励总额 | SUM(奖励 WHERE member_id IN 问题用户) | 表2+表3 |
| 问题用户参与活动数 | COUNT(DISTINCT member_id WHERE 问题用户 AND 参与活动) | 表2+表3 |

### 4.3 细分问题类型指标
| 指标名称 | 判断条件 | 去重规则 |
|----------|----------|----------|
| 同IP用户数 | ip_cnt > 5 | 按member_id去重 |
| 同设备ID用户数 | device_id_cnt > 2 | 按member_id去重 |
| 邮箱相似用户数 | similar_email = 'yes' | 按member_id去重 |
| 同设备ID或邮箱相似用户数 | device_id_cnt > 2 OR similar_email = 'yes' | 同一用户满足多条件只计算一次 |
| KYC后问题设备ID用户数 | kyc_level = 3 AND device_id_cnt > 2 | 按member_id去重 |
| KYC后问题邮箱相似用户数 | kyc_level = 3 AND similar_email = 'yes' | 按member_id去重 |

## 5. 技术实现方案

### 5.1 技术栈
- **编程语言**：Python 3.8+
- **数据处理**：pandas, numpy
- **日期处理**：datetime
- **输出格式**：CSV, 控制台显示

### 5.2 核心模块设计
```
user_data_analyzer/
├── main.py                 # 主程序入口
├── data_processor.py       # 数据处理模块
├── indicator_calculator.py # 指标计算模块
├── report_generator.py     # 报告生成模块
└── config.py              # 配置文件
```

### 5.3 数据处理流程
1. **数据加载**：读取三个CSV文件
2. **数据清洗**：日期格式转换、空值处理
3. **数据关联**：基于member_id进行多表关联
4. **指标计算**：按日期和国家维度计算各项指标
5. **结果输出**：生成分析报告

## 6. 输出规范

### 6.1 输出维度
- **时间维度**：按日期分组
- **地理维度**：按国家分组

### 6.2 输出字段
| 字段名 | 说明 |
|--------|------|
| 日期 | 统计日期 |
| 国家 | 用户国家 |
| 注册用户数 | 当日注册用户总数 |
| KYC用户数 | 当日KYC用户数 |
| 参与活动用户数 | 当日参与活动用户数 |
| 参与活动比例 | 参与活动用户占注册用户比例 |
| 活动奖励总额 | 当日发放奖励总额 |
| 问题用户数 | 当日发现问题用户数 |
| 问题用户比例 | 问题用户占注册用户比例 |
| 问题用户奖励总额 | 问题用户获得的奖励总额 |
| 问题用户参与活动数 | 问题用户中参与活动的人数 |
| 问题用户参与活动奖励 | 问题用户参与活动获得的奖励 |
| 同IP用户数 | ip_cnt > 5的用户数 |
| 同设备ID用户数 | device_id_cnt > 2的用户数 |
| 邮箱相似用户数 | similar_email = 'yes'的用户数 |
| KYC后问题设备ID用户数 | KYC后仍有设备ID问题的用户数 |
| KYC后问题邮箱相似用户数 | KYC后仍有邮箱相似问题的用户数 |

## 7. 使用说明

### 7.1 环境要求
- Python 3.8+
- pandas >= 1.3.0
- numpy >= 1.21.0

### 7.2 使用方法
```bash
python main.py --start_date "2025-06-01" --end_date "2025-06-15"
```

### 7.3 参数说明
- `--start_date`: 分析开始日期 (格式: YYYY-MM-DD)
- `--end_date`: 分析结束日期 (格式: YYYY-MM-DD)
- `--output_path`: 输出文件路径 (可选，默认: temp/analysis_result.csv)

## 8. 开发计划

### 8.1 开发阶段
1. **阶段1**：核心数据处理模块开发
2. **阶段2**：指标计算逻辑实现
3. **阶段3**：报告生成和输出功能
4. **阶段4**：测试和优化

### 8.2 交付物
- 完整的Python分析脚本
- 测试数据和用例
- 使用说明文档

## 9. 新增功能：问题用户访问渠道统计 (most_freq_via)

### 9.1 功能概述
基于注册总表中新增的`most_freq_via`字段，统计问题用户的访问渠道分布，分析APP和WEB用户的比例。

### 9.2 分类规则
- **APP类别**：包含 `Android`, `iOS`, `IOS`, `ANDROID`, `ios`
- **WEB类别**：包含 `WEB`, `H5`, `web`, `h5`
- **数据处理**：自动剔除空白和无效数据

### 9.3 统计指标
- 总有效用户数
- APP用户数量和比例
- WEB用户数量和比例
- 各渠道详细分布统计

### 9.4 使用方式

#### 方式1：集成在主程序中
```bash
python3 main.py --days 30
```
主程序会自动包含most_freq_via统计，并在控制台显示结果。

#### 方式2：独立分析器
```bash
python3 most_freq_via_analyzer.py
```
专门的分析器，只进行访问渠道统计分析。

### 9.5 输出结果
- **控制台显示**：实时查看统计结果
- **文件保存**：自动保存到 `output/most_freq_via_stats_YYYYMMDD_HHMMSS.txt`
- **格式化输出**：包含图标和百分比的友好显示

### 9.6 示例输出
```
问题用户访问渠道统计 (most_freq_via)
============================================================

总体统计:
----------------------------------------
总有效用户数: 44,690
APP用户数: 26,869 (60.12%)
WEB用户数: 17,821 (39.88%)

详细分类统计:
----------------------------------------
📱 Android: 24,463个 (54.74%) - APP
🌐 H5: 12,871个 (28.80%) - WEB
🌐 WEB: 4,950个 (11.08%) - WEB
📱 iOS: 2,406个 (5.38%) - APP
```
- 分析结果示例
