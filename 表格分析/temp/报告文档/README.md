# 用户数据分析系统

## 项目简介

这是一个用于分析平台用户注册、活动参与、问题用户等多维度数据的分析系统。系统可以按日期和国家维度统计各种用户行为指标，为业务决策提供数据支持。

## 功能特性

- 多表数据关联分析
- 问题用户自动识别和分类
- 按日期和国家维度统计
- KYC用户行为分析
- 活动参与度和奖励分析
- 多格式报告输出 (CSV, Excel, 文本)
- **🆕 问题用户访问渠道统计 (most_freq_via)**

## 安装和使用

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt
```

### 2. 数据准备

将以下三个CSV文件放在 `data/` 目录下：

- `register_users.csv` - 注册用户表
- `problem_users.csv` - 问题用户表  
- `welfare_users.csv` - 福利用户表

### 3. 运行分析

```bash
# 使用默认设置 (分析最近15天)
python main.py

# 指定日期范围
python main.py --start_date "2025-06-01" --end_date "2025-06-15"

# 分析最近30天
python main.py --days 30

# 指定数据文件路径
python main.py --data_path "/path/to/data"

# 指定输出文件路径
python main.py --output_path "/path/to/output.csv"
```

### 4. 创建示例数据

```bash
# 创建示例数据用于测试
python main.py --create-sample
```

## 数据格式要求

### 注册用户表 (register_users.csv)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| member_id | String | 用户ID |
| register_time | String | 注册时间 (格式: 2025/6/19 0:07:23) |
| member_country | String | 用户国家 |
| kyc_level | Integer | KYC等级 (1=未KYC, 3=已KYC) |

### 问题用户表 (problem_users.csv)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| member_id | String | 用户ID |
| create_time | String | 问题发现时间 |
| ip_cnt | Integer | IP地址数量 |
| device_id_cnt | Integer | 设备ID数量 |
| similar_email | String | 邮箱相似标识 (yes/no) |
| black_grey_flag | String | 黑灰名单标识 |
| risk_label | String | 风险标签 |
| user_risk_flag | String | 用户风险标识 |

### 福利用户表 (welfare_users.csv)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| member_id | String | 用户ID |
| dt | String | 奖励发放日期 (格式: 2025-06-20) |
| newuser_bonus | Float | 新用户奖励 |
| POSITION_bonus | Float | 持仓奖励 |

## 输出结果

系统会生成以下报告文件：

1. **详细统计报告** (`analysis_result_YYYYMMDD_HHMMSS.csv`)
   - 按日期和国家分组的详细指标统计

2. **汇总报告** (`summary_report_YYYYMMDD_HHMMSS.txt`)
   - 整体统计数据和按国家汇总

3. **Excel报告** (`analysis_result_YYYYMMDD_HHMMSS.xlsx`)
   - 包含详细统计、汇总统计、按国家汇总三个工作表

4. **🆕 访问渠道统计报告** (`most_freq_via_stats_YYYYMMDD_HHMMSS.txt`)
   - 问题用户的APP和WEB访问渠道分布统计

## 指标说明

### 基础指标
- 注册用户数：当日注册的用户总数
- KYC用户数：完成KYC认证的用户数
- 参与活动用户数：参与活动并获得奖励的用户数
- 参与活动比例：参与活动用户占注册用户的比例
- 活动奖励总额：当日发放的奖励总金额

### 问题用户指标
- 问题用户数：满足问题条件的用户数
- 问题用户比例：问题用户占注册用户的比例
- 同IP用户数：IP地址数量 > 5的用户
- 同设备ID用户数：设备ID数量 > 2的用户
- 邮箱相似用户数：邮箱相似标识为yes的用户
- KYC后问题用户：完成KYC后仍有问题的用户

## 问题用户判断规则

用户满足以下任一条件即被识别为问题用户：
- IP地址数量 > 5
- 设备ID数量 > 2  
- 邮箱相似标识 = 'yes'
- 黑灰名单标识包含"黑名单"或"灰名单"
- 风险标签非空
- 用户风险标识非空

## 项目结构

```
temp/
├── main.py                 # 主程序入口
├── config.py              # 配置文件
├── data_processor.py       # 数据处理模块
├── indicator_calculator.py # 指标计算模块
├── report_generator.py     # 报告生成模块
├── requirements.txt        # 依赖包列表
├── README.md              # 使用说明
├── data/                  # 数据文件目录
└── output/                # 输出文件目录
```

## 注意事项

1. 确保数据文件格式正确，日期格式严格按照要求
2. 系统会自动创建输出目录
3. 如果数据量较大，处理时间可能较长
4. 建议定期备份分析结果

## 🆕 新功能：问题用户访问渠道统计

### 功能说明
基于注册总表中的`most_freq_via`字段，统计问题用户的访问渠道分布，分析APP和WEB用户的比例。

### 使用方法

#### 方法1：集成在主程序中
```bash
python3 main.py --days 30
```
主程序会自动包含访问渠道统计，并在控制台显示结果。

#### 方法2：独立分析器
```bash
# 分析最近30天的问题用户（默认）
python3 most_freq_via_analyzer.py

# 分析最近15天的问题用户
python3 most_freq_via_analyzer.py --days 15

# 分析最近7天的问题用户
python3 most_freq_via_analyzer.py --days 7
```
专门的分析器，只进行访问渠道统计分析。

### 分类规则
- **APP类别**：Android, iOS, IOS, ANDROID, ios
- **WEB类别**：WEB, H5, web, h5
- **数据处理**：自动剔除空白和无效数据

### 输出示例
```
问题用户访问渠道统计 (most_freq_via)
============================================================

总体统计:
----------------------------------------
总有效用户数: 1,855
APP用户数: 1,362 (73.42%)
WEB用户数: 493 (26.58%)

详细分类统计:
----------------------------------------
📱 Android: 1,158个 (62.43%) - APP
🌐 WEB: 356个 (19.19%) - WEB
📱 iOS: 204个 (11.00%) - APP
🌐 H5: 137个 (7.39%) - WEB
```

**注意**：以上数据是基于最近30天内识别出的1,866个问题用户中有效most_freq_via数据的1,855个用户的统计结果。

## 技术支持

如有问题请检查：
1. 数据文件格式是否正确
2. 依赖包是否安装完整
3. 文件路径是否正确
4. 日期范围是否合理
5. Excel文件是否包含所需的工作表
