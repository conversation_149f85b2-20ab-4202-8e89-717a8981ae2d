#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相对波动率 + 金额靠近0的对敲检测算法
核心思想：对敲是方向相反的交易，至少有一方应该接近0
"""

from dataclasses import dataclass
from typing import Tuple, Optional
import math

@dataclass
class Position:
    """仓位信息"""
    real_profit: float  # 实际盈亏
    volume: float = 0   # 交易量/交易规模
    direction: str = '' # 方向

def calculate_volatility_closeness_hedge_score(pos_a: Position, pos_b: Position, 
                                             expected_scale: float = None) -> Tuple[float, dict]:
    """
    相对波动率 + 金额靠近0的对敲检测算法
    
    核心思路：
    1. 一盈一亏：使用原始公式（已经很好）
    2. 双盈/双亏：检查是否至少有一方接近0 + 相对波动率
    
    对敲特征：
    - 方向相反的交易行为
    - 至少有一方应该接近0（或都接近0）
    - 相对于交易规模的波动应该较小
    
    Args:
        pos_a: 仓位A
        pos_b: 仓位B  
        expected_scale: 预期交易规模
    
    Returns:
        Tuple[float, dict]: (最终评分, 详细信息)
    """
    
    # === 基础计算 ===
    total_profit = pos_a.real_profit + pos_b.real_profit
    profit_sum = abs(pos_a.real_profit) + abs(pos_b.real_profit)
    
    if profit_sum == 0:
        return 0.0, {
            'base_score': 0.0,
            'final_score': 0.0,
            'explanation': '两个仓位都无盈亏，无法判断对敲特征',
            'case_type': '无盈亏'
        }
    
    # 基础评分（原始公式）
    base_score = 1.0 - abs(total_profit) / profit_sum
    
    pos_a_profit = pos_a.real_profit
    pos_b_profit = pos_b.real_profit
    
    # === 分情况处理 ===
    if (pos_a_profit > 0 and pos_b_profit < 0) or (pos_a_profit < 0 and pos_b_profit > 0):
        # 一盈一亏：符合对敲的方向相反特征，直接使用原始公式
        case_type = "一盈一亏"
        final_score = base_score
        explanation = f"一盈一亏，符合对敲方向相反特征，评分:{final_score:.3f}"
        
    else:
        # 双盈或双亏：需要检查是否符合对敲特征
        if pos_a_profit > 0 and pos_b_profit > 0:
            case_type = "双盈"
        else:
            case_type = "双亏"
            
        final_score, explanation = _calculate_closeness_volatility_score(
            pos_a_profit, pos_b_profit, expected_scale, case_type
        )
    
    # 风险等级
    if final_score >= 0.8:
        risk_level = "高风险对敲"
    elif final_score >= 0.6:
        risk_level = "中等风险对敲"
    elif final_score >= 0.4:
        risk_level = "低风险对敲"
    else:
        risk_level = "正常交易"
    
    details = {
        'base_score': base_score,
        'final_score': final_score,
        'explanation': explanation,
        'case_type': case_type,
        'risk_level': risk_level,
        'total_profit': total_profit,
        'profit_sum': profit_sum
    }
    
    return final_score, details


def _calculate_closeness_volatility_score(profit_a, profit_b, expected_scale, case_type):
    """
    基于接近0程度 + 相对波动率计算评分
    
    核心逻辑：
    1. 至少有一方接近0（对敲特征）
    2. 相对波动率较小
    3. 如果两方都远离0且金额较大，不太可能是对敲
    """
    
    abs_a = abs(profit_a)
    abs_b = abs(profit_b)
    min_abs = min(abs_a, abs_b)
    max_abs = max(abs_a, abs_b)
    
    # === 推测交易规模 ===
    if expected_scale and expected_scale > 0:
        scale = expected_scale
    else:
        # 根据最大金额推测规模
        if max_abs <= 1:
            scale = 10.0
        elif max_abs <= 10:
            scale = 100.0
        elif max_abs <= 100:
            scale = 1000.0
        else:
            scale = max_abs * 10
    
    # === 因子1: 接近0程度评分 ===
    # 至少有一方接近0是对敲的重要特征
    if min_abs <= 0.1:
        closeness_score = 0.9
        closeness_desc = f"最小金额{min_abs:.2f}U很接近0"
    elif min_abs <= 1:
        closeness_score = 0.8
        closeness_desc = f"最小金额{min_abs:.1f}U接近0"
    elif min_abs <= 5:
        closeness_score = 0.6
        closeness_desc = f"最小金额{min_abs:.1f}U较接近0"
    elif min_abs <= 10:
        closeness_score = 0.4
        closeness_desc = f"最小金额{min_abs:.1f}U稍远离0"
    else:
        # 两方都远离0，不太符合对敲特征
        if max_abs >= 20:
            closeness_score = 0.1
            closeness_desc = f"两方都远离0(最小{min_abs:.1f}U，最大{max_abs:.1f}U)，不太符合对敲特征"
        else:
            closeness_score = 0.2
            closeness_desc = f"两方都稍远离0(最小{min_abs:.1f}U)"
    
    # === 因子2: 相对波动率评分 ===
    rel_a = abs_a / scale
    rel_b = abs_b / scale
    max_relative = max(rel_a, rel_b)
    
    if max_relative <= 0.01:  # ≤1%
        volatility_score = 0.9
        volatility_desc = f"相对波动很小(≤1%)"
    elif max_relative <= 0.05:  # ≤5%
        volatility_score = 0.8
        volatility_desc = f"相对波动较小(≤5%)"
    elif max_relative <= 0.1:  # ≤10%
        volatility_score = 0.6
        volatility_desc = f"相对波动适中(≤10%)"
    elif max_relative <= 0.2:  # ≤20%
        volatility_score = 0.4
        volatility_desc = f"相对波动较大(≤20%)"
    else:  # >20%
        volatility_score = 0.2
        volatility_desc = f"相对波动很大(>{max_relative:.1%})"
    
    # === 综合评分 ===
    # 相对波动率更重要（70%），接近0程度次要（30%）
    final_score = 0.3 * closeness_score + 0.7 * volatility_score
    
    # 详细说明
    explanation = (f"{case_type}，{closeness_desc}，{volatility_desc} "
                  f"(A:{rel_a:.1%}, B:{rel_b:.1%}, 规模{scale:.0f}U)，"
                  f"评分:{final_score:.3f}")
    
    return final_score, explanation


def test_closeness_volatility_algorithm():
    """测试接近0 + 相对波动率算法"""
    
    print("=== 相对波动率 + 金额靠近0的对敲检测算法测试 ===\n")
    print("核心思想：对敲是方向相反的交易行为")
    print("1. 一盈一亏：符合对敲特征，使用原始公式")
    print("2. 双盈/双亏：检查至少有一方接近0 + 相对波动率")
    print("3. 权重：接近0程度(60%) + 相对波动率(40%)\n")
    
    test_cases = [
        # 一盈一亏情况（符合对敲方向特征）
        (Position(10.0), Position(-9.5), 100.0, "一盈一亏-几乎抵消"),
        (Position(1.0), Position(-0.9), 100.0, "一盈一亏-小金额"),
        
        # 你的关键例子
        (Position(-0.03), Position(-1.0), 10.0, "双亏-你的例子(10U规模)"),
        (Position(-0.03), Position(-1.0), 100.0, "双亏-你的例子(100U规模)"),
        
        # 双亏情况：一方接近0
        (Position(-0.1), Position(-5.0), 100.0, "双亏-一方很接近0"),
        (Position(-1.0), Position(-10.0), 100.0, "双亏-一方接近0"),
        (Position(-2.0), Position(-3.0), 100.0, "双亏-都较接近0"),
        
        # 双亏情况：都远离0（应该低分）
        (Position(-20.0), Position(-20.0), 100.0, "双亏-都远离0且相同"),
        (Position(-15.0), Position(-25.0), 100.0, "双亏-都远离0"),
        (Position(-50.0), Position(-60.0), 100.0, "双亏-都很远离0"),
        
        # 双盈情况：一方接近0
        (Position(0.1), Position(5.0), 100.0, "双盈-一方很接近0"),
        (Position(1.0), Position(10.0), 100.0, "双盈-一方接近0"),
        (Position(2.0), Position(3.0), 100.0, "双盈-都较接近0"),
        
        # 双盈情况：都远离0（应该低分）
        (Position(20.0), Position(20.0), 100.0, "双盈-都远离0且相同"),
        (Position(15.0), Position(25.0), 100.0, "双盈-都远离0"),
        
        # 特殊情况
        (Position(-0.01), Position(-0.02), 100.0, "双亏-都极接近0"),
        (Position(-100.0), Position(-0.5), 1000.0, "双亏-大金额但一方接近0"),
    ]
    
    for i, (pos_a, pos_b, scale, description) in enumerate(test_cases, 1):
        score, details = calculate_volatility_closeness_hedge_score(pos_a, pos_b, scale)
        
        print(f"测试用例 {i}: {description}")
        print(f"仓位: A={pos_a.real_profit}, B={pos_b.real_profit} (规模{scale}U)")
        print(f"情况类型: {details['case_type']}")
        print(f"基础评分: {details['base_score']:.3f}")
        print(f"最终评分: {details['final_score']:.3f}")
        print(f"风险等级: {details['risk_level']}")
        print(f"说明: {details['explanation']}")
        print("-" * 80)


if __name__ == "__main__":
    test_closeness_volatility_algorithm()
