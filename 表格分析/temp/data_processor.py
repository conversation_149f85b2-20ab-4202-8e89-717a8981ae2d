# -*- coding: utf-8 -*-
"""
数据处理模块
负责数据加载、清洗、格式转换等功能
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
from config import *

class DataProcessor:
    def __init__(self):
        self.register_df = None
        self.problem_df = None
        self.welfare_df = None
        
    def load_data(self, data_path=''):
        """
        加载三张数据表
        """
        try:
            # 构建文件路径
            if not data_path:
                data_path = DATA_DIR
                
            register_file = os.path.join(data_path, DATA_FILES['register_users'])
            problem_file = os.path.join(data_path, DATA_FILES['problem_users'])
            welfare_file = os.path.join(data_path, DATA_FILES['welfare_users'])
            
            # 加载数据
            print("正在加载数据文件...")
            self.register_df = pd.read_csv(register_file, encoding='utf-8')
            self.problem_df = pd.read_csv(problem_file, encoding='utf-8')
            self.welfare_df = pd.read_csv(welfare_file, encoding='utf-8')
            
            print(f"注册用户表: {len(self.register_df)} 条记录")
            print(f"问题用户表: {len(self.problem_df)} 条记录")
            print(f"福利用户表: {len(self.welfare_df)} 条记录")
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {str(e)}")
            return False
    
    def clean_data(self):
        """
        数据清洗和格式转换
        """
        try:
            print("开始数据清洗...")
            
            # 清洗注册用户表
            self._clean_register_data()
            
            # 清洗问题用户表
            self._clean_problem_data()
            
            # 清洗福利用户表
            self._clean_welfare_data()
            
            print("数据清洗完成")
            return True
            
        except Exception as e:
            print(f"数据清洗失败: {str(e)}")
            return False
    
    def _clean_register_data(self):
        """清洗注册用户表"""
        # 转换日期格式
        self.register_df['register_date'] = pd.to_datetime(
            self.register_df['register_time']
        ).dt.date

        # 确保auth_level为整数，并重命名为kyc_level以保持兼容性
        self.register_df['kyc_level'] = self.register_df['auth_level'].astype(int)

        # 去除重复记录
        self.register_df = self.register_df.drop_duplicates(subset=['member_id'])
        
    def _clean_problem_data(self):
        """清洗问题用户表"""
        # 转换日期格式 - create_time可能有NaT值
        self.problem_df['create_date'] = pd.to_datetime(
            self.problem_df['create_time'], errors='coerce'
        ).dt.date

        # 转换注册时间
        self.problem_df['register_date'] = pd.to_datetime(
            self.problem_df['register_time']
        ).dt.date

        # 确保数值字段为整数
        self.problem_df['ip_cnt'] = pd.to_numeric(self.problem_df['ip_cnt'], errors='coerce').fillna(0).astype(int)
        self.problem_df['device_id_cnt'] = pd.to_numeric(self.problem_df['device_id_cnt'], errors='coerce').fillna(0).astype(int)

        # 处理字符串字段的空值
        string_columns = ['black_grey_flag', 'risk_label', 'similar_email', 'user_risk_flag']
        for col in string_columns:
            if col in self.problem_df.columns:
                self.problem_df[col] = self.problem_df[col].fillna('').astype(str)

        # 处理dt字段 - 转换为日期格式
        if 'dt' in self.problem_df.columns:
            self.problem_df['dt_date'] = pd.to_datetime(
                self.problem_df['dt'].astype(str), format='%Y%m%d', errors='coerce'
            ).dt.date
        
    def _clean_welfare_data(self):
        """清洗福利用户表"""
        # 转换日期格式 - dt字段是整数格式如20250707
        self.welfare_df['welfare_date'] = pd.to_datetime(
            self.welfare_df['dt'].astype(str),
            format='%Y%m%d'
        ).dt.date

        # 转换注册时间
        self.welfare_df['register_date'] = pd.to_datetime(
            self.welfare_df['register_time']
        ).dt.date

        # 确保奖励字段为数值型
        self.welfare_df['newuser_bonus'] = pd.to_numeric(self.welfare_df['newuser_bonus'], errors='coerce').fillna(0)
        self.welfare_df['POSITION_bonus'] = pd.to_numeric(self.welfare_df['POSITION_bonus'], errors='coerce').fillna(0)

        # 计算总奖励
        self.welfare_df['total_bonus'] = self.welfare_df['newuser_bonus'] + self.welfare_df['POSITION_bonus']
        
    def filter_by_date_range(self, start_date, end_date):
        """
        按日期范围过滤数据
        """
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            print(f"过滤日期范围: {start_date} 到 {end_date}")
            
            # 过滤注册用户表
            self.register_df = self.register_df[
                (self.register_df['register_date'] >= start_date) & 
                (self.register_df['register_date'] <= end_date)
            ]
            
            # 过滤问题用户表（按注册时间过滤）
            self.problem_df = self.problem_df[
                (self.problem_df['register_date'] >= start_date) & 
                (self.problem_df['register_date'] <= end_date)
            ]
            
            # 过滤福利用户表
            self.welfare_df = self.welfare_df[
                (self.welfare_df['welfare_date'] >= start_date) & 
                (self.welfare_df['welfare_date'] <= end_date)
            ]
            
            print(f"过滤后 - 注册用户: {len(self.register_df)}, 问题用户: {len(self.problem_df)}, 福利用户: {len(self.welfare_df)}")
            
            return True
            
        except Exception as e:
            print(f"日期过滤失败: {str(e)}")
            return False
    
    def get_processed_data(self):
        """
        获取处理后的数据
        """
        return {
            'register': self.register_df,
            'problem': self.problem_df,
            'welfare': self.welfare_df
        }
    
    def validate_data(self):
        """
        数据验证
        """
        issues = []
        
        # 检查必要字段是否存在
        required_fields = {
            'register': ['member_id', 'register_date', 'member_country', 'kyc_level'],
            'problem': ['member_id', 'ip_cnt', 'device_id_cnt', 'similar_email'],  # create_date可能为空
            'welfare': ['member_id', 'welfare_date', 'newuser_bonus', 'POSITION_bonus']
        }
        
        for table_name, fields in required_fields.items():
            df = getattr(self, f'{table_name}_df')
            missing_fields = [f for f in fields if f not in df.columns]
            if missing_fields:
                issues.append(f"{table_name}表缺少字段: {missing_fields}")
        
        # 检查数据是否为空
        if len(self.register_df) == 0:
            issues.append("注册用户表为空")
        if len(self.problem_df) == 0:
            issues.append("问题用户表为空")
        if len(self.welfare_df) == 0:
            issues.append("福利用户表为空")
            
        if issues:
            print("数据验证发现问题:")
            for issue in issues:
                print(f"- {issue}")
            return False
        else:
            print("数据验证通过")
            return True
