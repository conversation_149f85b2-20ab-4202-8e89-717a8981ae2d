# -*- coding: utf-8 -*-
"""
读取Excel数据并查看结构
"""

import pandas as pd
import os

def read_excel_data():
    """读取Excel文件中的三个工作表"""
    
    excel_file = '真实数据表格.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"文件 {excel_file} 不存在")
        return None
    
    try:
        # 读取Excel文件的所有工作表
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        print("Excel文件包含的工作表:")
        for sheet_name in excel_data.keys():
            print(f"- {sheet_name}")
        
        print("\n" + "="*60)
        
        # 查看每个工作表的结构
        for sheet_name, df in excel_data.items():
            print(f"\n工作表: {sheet_name}")
            print(f"数据行数: {len(df)}")
            print(f"数据列数: {len(df.columns)}")
            print("列名:")
            for i, col in enumerate(df.columns):
                print(f"  {i+1}. {col}")
            
            print("\n前5行数据:")
            print(df.head())
            
            print("\n数据类型:")
            print(df.dtypes)
            
            print("\n" + "-"*50)
        
        return excel_data
        
    except Exception as e:
        print(f"读取Excel文件失败: {str(e)}")
        return None

def save_to_csv(excel_data):
    """将Excel数据保存为CSV文件"""
    if not excel_data:
        return
    
    # 创建data目录
    os.makedirs('data', exist_ok=True)
    
    # 根据工作表内容判断对应关系
    sheet_names = list(excel_data.keys())
    
    # 保存文件的映射关系
    file_mapping = {}
    
    for i, (sheet_name, df) in enumerate(excel_data.items()):
        print(f"\n工作表 '{sheet_name}' 的列名:")
        for j, col in enumerate(df.columns):
            print(f"  {j+1}. {col}")
        
        # 根据列名判断是哪个表
        columns = df.columns.tolist()
        
        if 'member_id' in columns and 'register_time' in columns and 'kyc_level' in columns:
            filename = 'register_users.csv'
            print(f"  -> 识别为注册用户表")
        elif 'member_id' in columns and 'create_time' in columns and 'ip_cnt' in columns:
            filename = 'problem_users.csv'
            print(f"  -> 识别为问题用户表")
        elif 'member_id' in columns and 'dt' in columns and ('newuser_bonus' in columns or 'POSITION_bonus' in columns):
            filename = 'welfare_users.csv'
            print(f"  -> 识别为福利用户表")
        else:
            filename = f'sheet_{i+1}.csv'
            print(f"  -> 未识别，保存为 {filename}")
        
        # 保存为CSV
        csv_path = os.path.join('data', filename)
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        file_mapping[sheet_name] = filename
        print(f"  -> 已保存到: {csv_path}")
    
    print(f"\n文件映射关系:")
    for sheet, file in file_mapping.items():
        print(f"  {sheet} -> {file}")

if __name__ == "__main__":
    print("开始读取Excel数据...")
    excel_data = read_excel_data()
    
    if excel_data:
        print("\n开始转换为CSV文件...")
        save_to_csv(excel_data)
        print("\n数据处理完成!")
    else:
        print("数据读取失败!")
