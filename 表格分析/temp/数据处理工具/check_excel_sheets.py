#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def check_excel_sheets():
    """检查Excel文件中的工作表"""
    
    try:
        # 获取所有工作表名称
        excel_file = pd.ExcelFile('真实数据表格.xlsx')
        sheet_names = excel_file.sheet_names
        
        print("Excel文件中的工作表:")
        for i, sheet_name in enumerate(sheet_names):
            print(f"{i+1}. {sheet_name}")
        
        # 读取每个工作表的前几行来查看结构
        for sheet_name in sheet_names:
            print(f"\n=== 工作表: {sheet_name} ===")
            try:
                df = pd.read_excel('真实数据表格.xlsx', sheet_name=sheet_name, nrows=5)
                print(f"列名: {df.columns.tolist()}")
                print(f"数据量: {len(pd.read_excel('真实数据表格.xlsx', sheet_name=sheet_name))}")
                
                # 检查是否有most_freq_via字段
                if 'most_freq_via' in df.columns:
                    print("*** 发现most_freq_via字段! ***")
                    full_df = pd.read_excel('真实数据表格.xlsx', sheet_name=sheet_name)
                    print("most_freq_via字段的唯一值:")
                    print(full_df['most_freq_via'].value_counts(dropna=False))
                    
            except Exception as e:
                print(f"读取工作表 {sheet_name} 时出错: {e}")
                
    except Exception as e:
        print(f"处理Excel文件时出现错误: {e}")

if __name__ == "__main__":
    check_excel_sheets()
